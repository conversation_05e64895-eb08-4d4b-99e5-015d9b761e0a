/* SellerMySportsStrategies.css */

.video-status-container {
  padding: var(--section-padding);
  background-color: var(--white);
  font-family: "Poppins", sans-serif;
}

.video-status-container .video-table {
  width: 100%;

  font-size: var(--basefont);
  background-color: var(--white);

  border-radius: var(--border-radius-large);
}

.video-status-container .video-table th {
  padding: 12px 10px;
  text-align: left;

  vertical-align: middle;
}
.video-status-container .video-table td {
  padding: 12px 10px;
  text-align: left;
  border-top: 1px solid var(--light-gray);
  vertical-align: middle;
}
.video-status-container .video-doc {
  display: flex;
  align-items: center;
  gap: 12px;
}

.video-status-container .video-thumbnail {
  width: 50px;
  height: 35px;
  border-radius: var(--border-radius);
  overflow: hidden;
  flex-shrink: 0;
}

.video-status-container .video-thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.video-status-container .placeholder-thumb {
  width: 100%;
  height: 100%;
  background: var(--light-gray);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  color: var(--text-color);
}

.video-status-container .video-title {
  font-size: var(--basefont);
  font-weight: 500;
  color: var(--text-color);
  line-height: 1.4;
  max-width: 300px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

/* Toggle Switch */
.video-status-container .switch {
  position: relative;
  display: inline-block;
  width: 35px;
  height: 18px;
}

.video-status-container .switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.video-status-container .slider {
  position: absolute;
  cursor: pointer;
  background-color: var(--light-gray);
  border-radius: 22px;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  transition: 0.4s;
}

.video-status-container .slider::before {
  position: absolute;
  content: "";
  height: 12px;
  width: 12px;
  left: 2px;
  bottom: 1.8px;
  background-color: var(--white);
  border-radius: 50%;
  transition: 0.4s;
}

.video-status-container input:checked + .slider {
  background-color: var(--btn-color);
}

.video-status-container input:checked + .slider::before {
  transform: translateX(18px);
}

.video-status-container .slider.round {
  border-radius: 34px;
}

/* Action Icons Container */
.action-icon-container {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* Base Action Icon Styles */
.action-icon {
  font-size: var(--heading6) !important;
  cursor: pointer;
  transition: all 0.2s ease;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: transparent;
  
}

/* Eye Icon (View) */
.eyeicon {
  color: #6c757d;
    font-size: var(--heading6) !important;
  cursor: pointer;
  transition: all 0.2s ease;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: transparent;
}

.eyeicon:hover {
  color: var(--primary-color);
  background-color: var(--primary-light-color);
  transform: translateY(-2px);
}

/* Edit Icon */
.edit-icon {
  color: #28a745;
    font-size: var(--heading6) !important;
  cursor: pointer;
  transition: all 0.2s ease;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: transparent;
}

.edit-icon:hover {
  color: #1e7e34;
  background-color: rgba(40, 167, 69, 0.1);
  transform: translateY(-2px);
}

/* Delete Icon */
.delete-icon {
  color: #dc3545;
    font-size: var(--heading6) !important;
  cursor: pointer;
  transition: all 0.2s ease;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: transparent;
}

.delete-icon:hover {
  color: #c82333;
  background-color: rgba(220, 53, 69, 0.1);
  transform: translateY(-2px);
}

/* Loading and Empty States */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid var(--light-gray);
  border-top: 4px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.loading-container p {
  color: var(--text-color);
  font-size: var(--basefont);
  margin: 0;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
  background: var(--light-background);
  border-radius: var(--border-radius-large);
  border: 2px dashed var(--light-gray);
}

.empty-state h3 {
  color: var(--text-color);
  font-size: var(--heading5);
  margin: 0 0 12px 0;
  font-weight: 600;
}

.empty-state p {
  color: var(--text-light);
  font-size: var(--basefont);
  margin: 0;
  max-width: 400px;
  line-height: 1.5;
}

/* Disabled state for toggle switch */
.video-status-container input:disabled + .slider {
  opacity: 0.6;
  cursor: not-allowed;
}

.video-status-container input:disabled + .slider::before {
  cursor: not-allowed;
}