import api from './api';
import { AUTH_ENDPOINTS, STORAGE_KEYS } from '../utils/constants';

/**
 * Register a new user
 * @param {Object} userData - User registration data
 * @returns {Promise} Promise with registration response (includes userId for OTP verification)
 */
export const register = async (userData) => {
  const response = await api.post(AUTH_ENDPOINTS.REGISTER, userData);

  // Note: Registration returns userId for OTP verification flow
  // When <PERSON><PERSON> is disabled, backend still creates temp user and returns userId
  // The OTP will be displayed on verification screen

  return response.data;
};

/**
 * Login with mobile number (sends <PERSON><PERSON> or logs in directly if <PERSON><PERSON> disabled)
 * @param {Object} loginData - Object containing mobile number
 * @returns {Promise} Promise with login response (includes userId for OTP verification or token if <PERSON><PERSON> disabled)
 */
export const login = async (loginData) => {
  const response = await api.post(AUTH_ENDPOINTS.LOGIN, loginData);

  // If <PERSON><PERSON> is disabled, the response will contain a token and user data
  // If <PERSON><PERSON> is enabled, the response will contain userId for OTP verification
  if (response.data.token) {
    // OTP is disabled - store token and user data
    localStorage.setItem(STORAGE_KEYS.AUTH_TOKEN, response.data.token);

    if (response.data.user && response.data.user !== undefined) {
      localStorage.setItem(STORAGE_KEYS.USER, JSON.stringify(response.data.user));
    }
  }

  return response.data;
};

/**
 * Send OTP to user's email and/or mobile
 * @param {Object} data - Object containing email and/or mobile
 * @returns {Promise} Promise with success message
 */
export const sendOTP = async (data) => {
  const response = await api.post(AUTH_ENDPOINTS.SEND_OTP, data);
  return response.data;
};

/**
 * Verify OTP and complete login/registration
 * @param {Object} data - Object containing userId and otp
 * @returns {Promise} Promise with user data and token
 */
export const verifyOTP = async (data) => {
  const response = await api.post(AUTH_ENDPOINTS.VERIFY_OTP, data);

  // Store token and user data in localStorage after successful OTP verification
  if (response.data.token) {
    localStorage.setItem(STORAGE_KEYS.AUTH_TOKEN, response.data.token);

    // Only store user data if it exists and is not undefined
    if (response.data.user && response.data.user !== undefined) {
      localStorage.setItem(STORAGE_KEYS.USER, JSON.stringify(response.data.user));
    }
  }

  return response.data;
};

/**
 * Clear all localStorage data
 * This function ensures all app-related data is removed from localStorage
 */
export const clearAllLocalStorage = () => {
  // Clear auth-related data
  localStorage.removeItem(STORAGE_KEYS.AUTH_TOKEN);
  localStorage.removeItem(STORAGE_KEYS.USER);

  // Clear any other app-specific localStorage items
  // Add more items here if needed in the future
  const keysToRemove = [];

  // Get all localStorage keys and remove any that start with our app prefix
  for (let i = localStorage.length - 1; i >= 0; i--) {
    const key = localStorage.key(i);
    if (key && key.startsWith('xosportshub_')) {
      keysToRemove.push(key);
    }
  }

  // Remove all identified keys
  keysToRemove.forEach(key => localStorage.removeItem(key));
};

/**
 * Logout user
 * @returns {Promise} Promise with success message
 */
export const logout = async () => {
  try {
    // Call the logout endpoint
    const response = await api.get(AUTH_ENDPOINTS.LOGOUT);

    // Clear all localStorage data regardless of API response
    clearAllLocalStorage();

    return response.data;
  } catch (error) {
    // Even if API call fails, clear localStorage
    clearAllLocalStorage();
    throw error;
  }
};

/**
 * Get current user profile
 * @returns {Promise} Promise with user data
 */
export const getCurrentUser = async () => {
  const response = await api.get(AUTH_ENDPOINTS.ME);
  return response.data;
};

/**
 * Upload profile image
 * @param {File} file - Image file to upload
 * @returns {Promise} Promise with upload response
 */
export const uploadProfileImage = async (file) => {
  const formData = new FormData();
  formData.append('profileImage', file);

  const response = await api.post('/auth/upload', formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });

  return response.data;
};

/**
 * Update current user profile
 * @param {Object} userData - User data to update
 * @returns {Promise} Promise with updated user data
 */
export const updateCurrentUser = async (userData) => {
  const response = await api.put(AUTH_ENDPOINTS.UPDATE, userData);

  // Update localStorage with new user data
  if (response.data.data) {
    localStorage.setItem(STORAGE_KEYS.USER, JSON.stringify(response.data.data));
  }

  return response.data;
};

// Password-related functions removed as the system now uses OTP-based authentication

/**
 * Verify email with token
 * @param {string} token - Email verification token
 * @returns {Promise} Promise with success message
 */
export const verifyEmail = async (token) => {
  const response = await api.get(`${AUTH_ENDPOINTS.VERIFY_EMAIL}/${token}`);
  return response.data;
};

/**
 * Google Sign-In
 * @param {string} idToken - Firebase ID token from Google authentication
 * @returns {Promise} Promise with user data and token
 */
export const googleSignIn = async (idToken) => {
  const response = await api.post(AUTH_ENDPOINTS.GOOGLE_AUTH, { idToken });

  // Store token and user data in localStorage after successful authentication
  if (response.data.token) {
    localStorage.setItem(STORAGE_KEYS.AUTH_TOKEN, response.data.token);

    if (response.data.user && response.data.user !== undefined) {
      localStorage.setItem(STORAGE_KEYS.USER, JSON.stringify(response.data.user));
    }
  }

  return response.data;
};

/**
 * Google Sign-Up with role selection
 * @param {string} idToken - Firebase ID token from Google authentication
 * @param {string} role - Selected user role (buyer/seller)
 * @returns {Promise} Promise with user data and token
 */
export const googleSignUp = async (idToken, role) => {
  const response = await api.post(AUTH_ENDPOINTS.GOOGLE_SIGNUP, { idToken, role });

  // Store token and user data in localStorage after successful registration
  if (response.data.token) {
    localStorage.setItem(STORAGE_KEYS.AUTH_TOKEN, response.data.token);

    if (response.data.user && response.data.user !== undefined) {
      localStorage.setItem(STORAGE_KEYS.USER, JSON.stringify(response.data.user));
    }
  }

  return response.data;
};

/**
 * Toggle user role between buyer and seller
 * @returns {Promise} Promise with updated user data
 */
export const toggleRole = async () => {
  const response = await api.post(AUTH_ENDPOINTS.TOGGLE_ROLE);

  // Update user data in localStorage after successful role toggle
  if (response.data.data) {
    localStorage.setItem(STORAGE_KEYS.USER, JSON.stringify(response.data.data));
  }

  return response.data;
};

/**
 * Check if user is authenticated
 * @returns {boolean} True if user is authenticated
 */
export const isAuthenticated = () => {
  const token = localStorage.getItem(STORAGE_KEYS.AUTH_TOKEN);
  return !!token;
};

/**
 * Get user from localStorage
 * @returns {Object|null} User object or null
 */
export const getStoredUser = () => {
  try {
    const user = localStorage.getItem(STORAGE_KEYS.USER);
    // Check if user exists and is not "undefined" string
    if (user && user !== 'undefined' && user !== 'null') {
      return JSON.parse(user);
    }
    return null;
  } catch (error) {
    console.error('Error parsing user data from localStorage:', error);
    // Clear invalid data
    localStorage.removeItem(STORAGE_KEYS.USER);
    return null;
  }
};

export default {
  register,
  login,
  sendOTP,
  verifyOTP,
  logout,
  clearAllLocalStorage,
  getCurrentUser,
  uploadProfileImage,
  updateCurrentUser,
  verifyEmail,
  googleSignIn,
  googleSignUp,
  toggleRole,
  isAuthenticated,
  getStoredUser,
};
