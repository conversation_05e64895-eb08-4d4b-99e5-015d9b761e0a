/* SellerRequests Component Styles */
.seller-requests-container {
  padding: var(--section-padding);
  background-color: var(--white);
  font-family: "Poppins", sans-serif;
}

.requests-table {
  width: 100%;
  font-size: var(--basefont);
  background-color: var(--white);

  border-radius: var(--border-radius-large);
  overflow: hidden;
}

.requests-table th {
  padding: 12px 10px;
  text-align: left;
  vertical-align: middle;
}

.requests-table td {
  padding: 12px 10px;
  text-align: left;
  border-top: 1px solid var(--light-gray);
  vertical-align: middle;
}

.video-doc {
  display: flex;
  align-items: center;
  gap: 10px;
}

.video-doc img {
  width: 55px;
  height: 55px;
  border-radius: var(--border-radius);
  object-fit: cover;
}

.video-doc span {
  font-size: var(--smallfont);
  font-weight: 500;
  color: var(--text-color);
}

/* Action icons */
.action-icons {

  display: flex;
  gap: var(--smallfont);  
  align-items: center;
  justify-content: center;
}

.eye-icon, .comment-icon {
  font-size: var(--heading6);
  color: black;
  cursor: pointer;
  transition: all 0.2s ease;

  display: flex;
  align-items: center;
  justify-content: center;
  background-color: transparent;
}

.action-icon:hover {
  color: var(--primary-color);
  background-color: var(--primary-light-color);
  transform: translateY(-2px);
}
.eye-icon:hover ,.comment-icon:hover{
  color: var(--primary-color);
  background-color: var(--primary-light-color);
  transform: translateY(-2px);
}