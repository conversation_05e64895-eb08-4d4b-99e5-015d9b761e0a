import { toast } from 'react-toastify';
import authService from '../services/authService';

/**
 * Navigation utility functions for handling authentication and role-based access
 */

/**
 * Check if user is authenticated
 * @returns {boolean} True if user is authenticated
 */
export const isUserAuthenticated = () => {
  return authService.isAuthenticated();
};

/**
 * Get current user data
 * @returns {Object|null} User object or null
 */
export const getCurrentUser = () => {
  return authService.getStoredUser();
};

/**
 * Get user role
 * @returns {string} User role or 'visitor'
 */
export const getUserRole = () => {
  const user = getCurrentUser();
  return user?.role || 'visitor';
};

/**
 * Get effective user role (activeRole for non-admin, role for admin)
 * @returns {string} Effective user role or 'visitor'
 */
export const getEffectiveUserRole = () => {
  const user = getCurrentUser();
  if (!user) return 'visitor';

  // Admin users use their role, non-admin users use activeRole
  return user.role === 'admin' ? user.role : (user.activeRole || user.role);
};

/**
 * Check if user can access buyer routes
 * @returns {boolean} True if user can access buyer routes
 */
export const canAccessBuyerRoutes = () => {
  const effectiveRole = getEffectiveUserRole();
  return isUserAuthenticated() && (effectiveRole === 'buyer' || effectiveRole === 'admin');
};

/**
 * Check if user can access seller routes
 * @returns {boolean} True if user can access seller routes
 */
export const canAccessSellerRoutes = () => {
  const effectiveRole = getEffectiveUserRole();
  return isUserAuthenticated() && (effectiveRole === 'seller' || effectiveRole === 'admin');
};

/**
 * Handle Buy tab navigation with authentication and role checks
 * @param {Function} navigate - React Router navigate function
 * @returns {boolean} True if navigation was successful
 */
export const handleBuyNavigation = (navigate) => {
  if (!isUserAuthenticated()) {
    toast.error('Please sign in to access buyer features');
    navigate('/auth');
    return false;
  }

  const user = getCurrentUser();
  const userRole = user?.role;
  const effectiveRole = getEffectiveUserRole();

  if (userRole === 'admin') {
    toast.info('Admin users can access all features');
    navigate('/buyer/dashboard');
    return true;
  }

  // For non-admin users
  if (effectiveRole === 'buyer') {
    navigate('/buyer/account/dashboard');
  } else {
    // This covers users whose activeRole is not 'buyer' (e.g., 'seller').
    // Navigate to the general buyer dashboard, which might handle role switching
    // as per the original comment.
    navigate('/buyer/dashboard');
  }
  return true;
};

/**
 * Handle Sell tab navigation with authentication and role checks
 * @param {Function} navigate - React Router navigate function
 * @returns {boolean} True if navigation was successful
 */
export const handleSellNavigation = (navigate) => {
  if (!isUserAuthenticated()) {
    toast.error('Please sign in to access seller features');
    navigate('/auth');
    return false;
  }

  const user = getCurrentUser();
  const userRole = user?.role;
  const effectiveRole = getEffectiveUserRole();

  if (userRole === 'admin') {
    toast.info('Admin users can access all features');
    navigate('/seller/dashboard');
    return true;
  }

  // For non-admin users, allow navigation to seller dashboard
  // The role toggle will handle switching to seller mode if needed
  navigate('/seller/dashboard');
  return true;
};

/**
 * Get appropriate dashboard route for user role
 * @returns {string} Dashboard route path
 */
export const getUserDashboardRoute = () => {
  const user = getCurrentUser();
  const userRole = user?.role;
  const effectiveRole = getEffectiveUserRole();

  if (userRole === 'admin') {
    return '/admin/dashboard';
  }

  switch (effectiveRole) {
    case 'buyer':
      return '/buyer/account/dashboard';
    case 'seller':
      return '/seller/dashboard';
    default:
      return '/';
  }
};

/**
 * Check if current route matches user's role
 * @param {string} pathname - Current route pathname
 * @returns {boolean} True if route is appropriate for user role
 */
export const isRouteAuthorizedForUser = (pathname) => {
  const user = getCurrentUser();
  const userRole = user?.role;
  const effectiveRole = getEffectiveUserRole();

  if (!isUserAuthenticated()) {
    return true; // Allow public routes for non-authenticated users
  }

  if (userRole === 'admin') {
    return true; // Admin can access all routes
  }

  if (pathname.startsWith('/buyer/') && effectiveRole !== 'buyer') {
    return false;
  }

  if (pathname.startsWith('/seller/') && effectiveRole !== 'seller') {
    return false;
  }

  return true;
};

/**
 * Handle unauthorized route access
 * @param {string} pathname - Attempted route pathname
 * @param {Function} navigate - React Router navigate function
 */
export const handleUnauthorizedAccess = (pathname, navigate) => {
  const user = getCurrentUser();
  const userRole = user?.role;
  const effectiveRole = getEffectiveUserRole();

  if (userRole === 'admin') {
    // Admin can access all routes
    return;
  }

  if (pathname.startsWith('/buyer/') && effectiveRole === 'seller') {
    toast.error('Switch to buyer mode to access buyer features.');
    navigate('/seller/dashboard');
  } else if (pathname.startsWith('/seller/') && effectiveRole === 'buyer') {
    toast.error('Switch to seller mode to access seller features.');
    navigate('/buyer/dashboard');
  } else {
    toast.error('Unauthorized access');
    navigate(getUserDashboardRoute());
  }
};
