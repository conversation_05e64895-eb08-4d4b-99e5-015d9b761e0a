const express = require("express");
const path = require("path");
const fs = require("fs");
const https = require("https");
const dotenv = require("dotenv");
const cors = require("cors");
const helmet = require("helmet");
const morgan = require("morgan");
const connectDB = require("./config/db");
const errorHandler = require("./middleware/errorHandler");
const { initializeStorage } = require("./utils/storageHelper");

// Load environment variables
dotenv.config();

// Connect to database
console.log("MongoDB URI:", process.env.MONGODB_URI);
connectDB();

// Initialize storage system
initializeStorage();

// Initialize Express app
const app = express();

// Body parser with increased limits for large file uploads
app.use(express.json({ limit: '50mb' }));
app.use(express.urlencoded({ extended: true, limit: '50mb' }));

// Security middleware
app.use(
  helmet({
    contentSecurityPolicy: {
      directives: {
        defaultSrc: ["'self'"],
        frameAncestors: [
          "'self'",
          "http://localhost:5173",
          "http://127.0.0.1:5173",
          "https://xosportshub.thefabaf.com",
          "https://xosports.thefabaf.com",
          "http://xosportshub.thefabaf.com",
          "http://xosports.thefabaf.com",
        ],
      },
    },
    crossOriginResourcePolicy: { policy: "cross-origin" },
    crossOriginOpenerPolicy: { policy: "same-origin-allow-popups" },
  })
);


// CORS configuration
const corsOptions = {
  origin: [
    "http://localhost:3000",
    "http://localhost:5173",
    "http://127.0.0.1:3000",
    "http://127.0.0.1:5173",
    "https://localhost:3000",
    "https://localhost:5173",
    "https://xosportshub.thefabaf.com",
    "https://xosports.thefabaf.com",
    "http://xosportshub.thefabaf.com",
    "http://xosports.thefabaf.com",
    process.env.FRONTEND_URL,
  ].filter(Boolean),
  credentials: true,
  methods: ["GET", "POST", "PUT", "DELETE", "PATCH", "OPTIONS"],
  allowedHeaders: [
    "Content-Type",
    "Authorization",
    "X-Requested-With",
    "Accept",
    "Origin",
  ],
};

app.use(cors(corsOptions));

// Handle preflight requests for large uploads
app.options('*', (req, res) => {
  res.header('Access-Control-Allow-Origin', req.headers.origin);
  res.header('Access-Control-Allow-Methods', 'GET,PUT,POST,DELETE,OPTIONS');
  res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization, Content-Length, X-Requested-With');
  res.header('Access-Control-Allow-Credentials', 'true');
  res.header('Access-Control-Max-Age', '86400'); // 24 hours
  res.sendStatus(200);
});

// Logging middleware
if (process.env.NODE_ENV === "development") {
  app.use(morgan("dev"));
}

// Serve static files from "uploads" folder with CORP headers
app.use(
  "/uploads",
  express.static(path.join(__dirname, "uploads"), {
    setHeaders: (res, filePath) => {
      res.setHeader("Cross-Origin-Resource-Policy", "cross-origin"); // Allow usage in cross-origin iframes/images
      res.setHeader("Access-Control-Allow-Origin", "*"); // Optional: Allow any domain (for images only, not sensitive data)
    },
  })
);

// Define routes
app.use("/api/auth", require("./routes/auth"));
app.use("/api/users", require("./routes/users"));
app.use("/api/content", require("./routes/content"));
app.use("/api/orders", require("./routes/orders"));
app.use("/api/bids", require("./routes/bids"));
app.use("/api/requests", require("./routes/requests"));
app.use("/api/payments", require("./routes/payments"));
app.use("/api/cards", require("./routes/cards"));
app.use("/api/notifications", require("./routes/notifications"));
app.use("/api/cms", require("./routes/cms"));
app.use("/api/settings", require("./routes/settings"));
app.use("/api/dashboard", require("./routes/dashboard"));
app.use("/api/reviews", require("./routes/reviews"));
app.use("/api/messages", require("./routes/messages"));
app.use("/api/document-preview", require("./routes/documentPreview"));

// Diagnostics routes (development and testing)
if (process.env.NODE_ENV !== 'production') {
  app.use("/api/diagnostics", require("./routes/diagnostics"));
}
app.use("/api/wishlist", require("./routes/wishlist"));

// Health check route
app.get("/health", (req, res) => {
  res.status(200).json({ status: "ok" });
});

// Error handling middleware
app.use(errorHandler);

// Server configuration based on environment
let server;
const PORT = process.env.PORT || 5000;
const isLocal = process.env.NODE_ENV === 'development' && (process.env.USE_HTTPS !== 'true');

if (isLocal) {
  // Use HTTP for local development
  server = app.listen(PORT, () => {
    console.log(`HTTP Server running in ${process.env.NODE_ENV} mode on port ${PORT}`);
    console.log(`Server URL: http://localhost:${PORT}`);
  });

  // Set timeout for long uploads (10 minutes)
  server.timeout = 600000; // 10 minutes
  server.keepAliveTimeout = 65000; // 65 seconds
  server.headersTimeout = 66000; // 66 seconds
} else {
  // Use HTTPS for development/production environments
  try {
    // Read SSL certificate files
    const privateKey = fs.readFileSync(path.join(__dirname, 'secret', 'key.pem'), 'utf8');
    const certificate = fs.readFileSync(path.join(__dirname, 'secret', 'crt.pem'), 'utf8');

    // SSL credentials
    const credentials = {
      key: privateKey,
      cert: certificate
    };

    // Create HTTPS server
    server = https.createServer(credentials, app);

    server.listen(PORT, () => {
      console.log(`HTTPS Server running in ${process.env.NODE_ENV} mode on port ${PORT}`);
      console.log(`Server URL: https://localhost:${PORT}`);
    });

    // Set timeout for long uploads (10 minutes)
    server.timeout = 600000; // 10 minutes
    server.keepAliveTimeout = 65000; // 65 seconds
    server.headersTimeout = 66000; // 66 seconds
  } catch (error) {
    console.warn('SSL certificates not found or invalid, falling back to HTTP server');
    console.warn('Error:', error.message);

    // Fallback to HTTP server if SSL certificates are not available
    server = app.listen(PORT, () => {
      console.log(`HTTP Server (fallback) running in ${process.env.NODE_ENV} mode on port ${PORT}`);
      console.log(`Server URL: http://localhost:${PORT}`);
    });

    // Set timeout for long uploads (10 minutes)
    server.timeout = 600000; // 10 minutes
    server.keepAliveTimeout = 65000; // 65 seconds
    server.headersTimeout = 66000; // 66 seconds
  }
}

// Handle unhandled promise rejections
process.on("unhandledRejection", (err, promise) => {
  console.log(`Error: ${err.message}`);
  // Close server & exit process
  server.close(() => process.exit(1));
});
