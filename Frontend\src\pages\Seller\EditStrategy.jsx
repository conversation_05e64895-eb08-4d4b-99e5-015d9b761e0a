import React, { useState, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useParams, useNavigate } from "react-router-dom";
import SellerLayout from "../../components/seller/SellerLayout";
import { FiUpload } from "react-icons/fi";
import { MdPlayArrow, MdDownload } from "react-icons/md";
import {
  getSellerContentById,
  updateContent,
  uploadContentFile
} from "../../redux/slices/contentSlice";
import SummernoteEditor from "../../components/common/SummernoteEditor";
import UploadProgressBar from "../../components/common/UploadProgressBar";
import DocumentViewer from "../../components/common/DocumentViewer";
import { showSuccess, showError } from "../../utils/toast";
import { getImageUrl, getPlaceholderImage, getSmartFileUrl } from "../../utils/constants";
import {
  validateFileByContentType,
  getAcceptAttribute,
  isFileUploadDisabled,
  getFileUploadPlaceholder
} from "../../utils/fileValidation";

import "../../styles/AddStrategy.css";

const EditStrategy = () => {
  const { id } = useParams();
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { singleContent, isLoading } = useSelector((state) => state.content);

  // State for array field inputs
  const [newTag, setNewTag] = useState("");

  // Form state - Visible fields as per screenshot
  const [formData, setFormData] = useState({
    // Visible fields
    title: "",
    category: "",
    coachName: "",
    description: "",
    fileUrl: "",
    aboutCoach: "",
    strategicContent: "",

    // Hidden fields with default values for backend compatibility
    sport: "Other",
    contentType: "Video",
    previewUrl: "",
    thumbnailUrl: "",
    duration: "",
    videoLength: "",
    fileSize: "",
    tags: [],
    difficulty: "Intermediate",
    language: "English",
    prerequisites: [],
    learningObjectives: [],
    equipment: [],
    saleType: "Fixed",
    price: 0,
    allowCustomRequests: false,
    customRequestPrice: "",
    status: "Published",
    visibility: "Public"
  });

  // File upload state
  const [uploadedFile, setUploadedFile] = useState(null);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Upload progress states
  const [showUploadProgress, setShowUploadProgress] = useState(false);
  const [currentUploadType, setCurrentUploadType] = useState('');
  const [currentFileName, setCurrentFileName] = useState('');

  // Thumbnail validation states
  const [thumbnailError, setThumbnailError] = useState('');
  const [showThumbnailPreview, setShowThumbnailPreview] = useState(true);

  // Validation state
  const [validationErrors, setValidationErrors] = useState({});
  const [showValidation, setShowValidation] = useState(false);
  const [formSubmitted, setFormSubmitted] = useState(false);

  // Add state to track if we've attempted to fetch data
  const [hasFetchedData, setHasFetchedData] = useState(false);
  const [fetchError, setFetchError] = useState(null);

  // Fetch content data when component mounts
  useEffect(() => {
    if (id && !hasFetchedData) {
      setHasFetchedData(true);
      setFetchError(null);

      dispatch(getSellerContentById(id))
        .unwrap()
        .then((response) => {
        })
        .catch((error) => {
          console.error('Failed to fetch strategy data:', error); // Debug log
          setFetchError(error.message || 'Failed to load strategy data');
          showError('Failed to load strategy data. Please try again.');
        });
    }
  }, [dispatch, id, hasFetchedData]);

  // Populate form when content is loaded
  useEffect(() => {
    if (singleContent) {
      setFormData({
        title: singleContent.title || "",
        category: singleContent.category || "",
        coachName: singleContent.coachName || "",
        description: singleContent.description || "",
        fileUrl: singleContent.fileUrl || "",
        aboutCoach: singleContent.aboutCoach || "",
        strategicContent: singleContent.strategicContent || "",

        // Hidden fields
        sport: singleContent.sport || "Other",
        contentType: singleContent.contentType || "Video",
        previewUrl: singleContent.previewUrl || "",
        thumbnailUrl: singleContent.thumbnailUrl || "",
        duration: singleContent.duration || "",
        videoLength: singleContent.videoLength || "",
        fileSize: singleContent.fileSize || "",
        tags: singleContent.tags || [],
        difficulty: singleContent.difficulty || "Intermediate",
        language: singleContent.language || "English",
        prerequisites: singleContent.prerequisites || [],
        learningObjectives: singleContent.learningObjectives || [],
        equipment: singleContent.equipment || [],
        saleType: singleContent.saleType || "Fixed",
        price: singleContent.price || 0,
        allowCustomRequests: singleContent.allowCustomRequests || false,
        customRequestPrice: singleContent.customRequestPrice || "",
        status: singleContent.status || "Draft",
        visibility: singleContent.visibility || "Public"
      });
    }
  }, [singleContent]);

  // Handle input changes with validation
  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));

    // Validate field on change
    validateField(name, type === 'checkbox' ? checked : value);
  };

  // Handle Summernote changes
  const handleSummernoteChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));

    // Validate field on change
    validateField(field, value);
  };

  // Handle field blur event
  const handleFieldBlur = (e) => {
    const { name, value } = e.target;
    validateField(name, value);
  };

  // Validate individual field
  const validateField = (fieldName, value) => {
    const errors = { ...validationErrors };

    switch (fieldName) {
      case 'title':
        if (!value.trim()) {
          errors.title = 'Strategy title is required';
        } else {
          delete errors.title;
        }
        break;

      case 'category':
        if (!value) {
          errors.category = 'Please select a category';
        } else {
          delete errors.category;
        }
        break;

      case 'coachName':
        if (!value.trim()) {
          errors.coachName = 'Coach/Seller/Academy name is required';
        } else {
          delete errors.coachName;
        }
        break;

      case 'description': {
        const cleanDescription = value.replace(/<[^>]*>/g, '').trim();
        if (!cleanDescription) {
          errors.description = 'Strategy description is required';
        } else {
          delete errors.description;
        }
        break;
      }

      case 'aboutCoach': {
        const cleanAboutCoach = value.replace(/<[^>]*>/g, '').trim();
        if (!cleanAboutCoach) {
          errors.aboutCoach = 'About the coach information is required';
        } else {
          delete errors.aboutCoach;
        }
        break;
      }

      case 'strategicContent': {
        const cleanStrategicContent = value.replace(/<[^>]*>/g, '').trim();
        if (!cleanStrategicContent) {
          errors.strategicContent = 'Strategic content description is required';
        } else {
          delete errors.strategicContent;
        }
        break;
      }

      case 'contentType':
        if (!value) {
          errors.contentType = 'Please select a content type';
        } else {
          delete errors.contentType;
        }
        break;

      case 'difficulty':
        if (!value) {
          errors.difficulty = 'Please select a difficulty level';
        } else {
          delete errors.difficulty;
        }
        break;

      case 'saleType':
        if (!value) {
          errors.saleType = 'Please select a sale type';
        } else {
          delete errors.saleType;
        }
        break;

      case 'price':
        if (!value || value <= 0) {
          errors.price = 'Please enter a valid price greater than $0';
        } else {
          delete errors.price;
        }
        break;

      default:
        break;
    }

    setValidationErrors(errors);
  };

  // Handle file upload
  const handleFileUpload = async (e) => {
    const file = e.target.files[0];
    if (!file) return;

    // Validate file based on selected content type
    const validation = validateFileByContentType(file, formData.contentType);
    if (!validation.isValid) {
      showError(validation.message);
      e.target.value = ''; // Clear the file input
      return;
    }

    setShowUploadProgress(true);
    setCurrentUploadType('content file');
    setCurrentFileName(file.name);

    try {
      const formDataUpload = new FormData();
      formDataUpload.append('file', file);

      const response = await dispatch(uploadContentFile(formDataUpload)).unwrap();

      setFormData(prev => ({
        ...prev,
        fileUrl: response.data.fileUrl,
        fileSize: response.data.fileSize
      }));

      setUploadedFile({
        name: response.data.fileName,
        url: response.data.fileUrl,
        type: response.data.fileType,
        size: response.data.fileSize
      });

      showSuccess('File uploaded successfully!');
    } catch (error) {
      console.error('File upload failed:', error);
      showError('Failed to upload file. Please try again.');
    } finally {
      setShowUploadProgress(false);
      setCurrentUploadType('');
      setCurrentFileName('');
    }
  };

  // Handle thumbnail upload
  const handleThumbnailUpload = async (e) => {
    const file = e.target.files[0];
    if (!file) return;

    // Clear previous errors
    setThumbnailError('');
    setShowThumbnailPreview(false);

    try {
      // Validate file type
      const validTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
      if (!validTypes.includes(file.type)) {
        throw new Error('Only JPG, PNG, and GIF formats are supported for thumbnails');
      }

      // Validate file size (max 5MB)
      const maxSize = 5 * 1024 * 1024; // 5MB
      if (file.size > maxSize) {
        throw new Error('Thumbnail file size must be less than 5MB');
      }

      // Show upload progress
      setShowUploadProgress(true);
      setCurrentUploadType('thumbnail');
      setCurrentFileName(file.name);

      const formDataUpload = new FormData();
      formDataUpload.append('file', file);
      formDataUpload.append('type', 'thumbnail');

      const response = await dispatch(uploadContentFile(formDataUpload)).unwrap();

      if (!response.data || !response.data.fileUrl) {
        throw new Error('Invalid response from server');
      }

      // Store the complete URL path
      const thumbnailUrl = response.data.fileUrl;
      // Update form data with the new thumbnail URL
      setFormData(prev => ({
        ...prev,
        thumbnailUrl: thumbnailUrl
      }));

      setShowThumbnailPreview(true);
      showSuccess('Thumbnail uploaded successfully!');
    } catch (error) {
      console.error('Thumbnail upload failed:', error);
      setThumbnailError(error.message || 'Failed to upload thumbnail. Please try again.');
      setFormData(prev => ({
        ...prev,
        thumbnailUrl: ''
      }));
    } finally {
      setShowUploadProgress(false);
      setCurrentUploadType('');
      setCurrentFileName('');
    }
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();
    setFormSubmitted(true);
    setIsSubmitting(true);

    // Validate all fields before submission
    const allErrors = {};

    if (!formData.title.trim()) {
      allErrors.title = 'Strategy title is required';
    }

    if (!formData.category) {
      allErrors.category = 'Please select a category';
    }

    if (!formData.coachName.trim()) {
      allErrors.coachName = 'Coach/Seller/Academy name is required';
    }

    if (!formData.description.replace(/<[^>]*>/g, '').trim()) {
      allErrors.description = 'Strategy description is required';
    }

    if (!formData.contentType) {
      allErrors.contentType = 'Please select a content type';
    }

    if (!formData.fileUrl && !uploadedFile) {
      allErrors.fileUpload = 'Please upload a video or document file';
    }

    if (!formData.aboutCoach.replace(/<[^>]*>/g, '').trim()) {
      allErrors.aboutCoach = 'About the coach information is required';
    }

    if (!formData.strategicContent.replace(/<[^>]*>/g, '').trim()) {
      allErrors.strategicContent = 'Strategic content description is required';
    }

    if (!formData.thumbnailUrl) {
      allErrors.thumbnailUpload = 'Please upload a thumbnail image';
    }

    if (!formData.difficulty) {
      allErrors.difficulty = 'Please select a difficulty level';
    }

    if (!formData.saleType) {
      allErrors.saleType = 'Please select a sale type';
    }

    if (!formData.price || formData.price <= 0) {
      allErrors.price = 'Please enter a valid price greater than $0';
    }

    // If there are validation errors, show them and stop submission
    if (Object.keys(allErrors).length > 0) {
      setValidationErrors(allErrors);
      setShowValidation(true);
      setIsSubmitting(false);

      // Scroll to the first error field
      setTimeout(() => {
        const firstErrorField = document.querySelector('.AddStrategy__validation-error');
        if (firstErrorField) {
          firstErrorField.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }
      }, 100);

      return;
    }

    try {
      // Prepare data for submission
      const submitData = {
        ...formData,
        sport: formData.category || "Other",
        coachName: formData.coachName || "Coach",
        thumbnailUrl: formData.thumbnailUrl
      };

      await dispatch(updateContent({ id, contentData: submitData })).unwrap();

      showSuccess('🎉 Strategy updated successfully!');
      navigate(`/seller/strategy-details/${id}`);

    } catch (error) {
      console.error('Content update failed:', error);
      let errorMessage = 'Failed to update strategy. Please try again.';

      if (error.message) {
        errorMessage = error.message;
      } else if (error.errors && error.errors.length > 0) {
        errorMessage = error.errors[0].msg || errorMessage;
      } else if (typeof error === 'string') {
        errorMessage = error;
      }

      showError(`❌ ${errorMessage}`);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Show loading state while fetching content
  if ((isLoading && !singleContent) || (!singleContent && !hasFetchedData)) {
    return (
      <SellerLayout>
        <div className="AddStrategy">
          <div className="loading-container">
            <div className="loading-spinner"></div>
            <p>Loading strategy details...</p>
          </div>
        </div>
      </SellerLayout>
    );
  }

  // Show error state if content not found after fetch attempt
  if (!singleContent && hasFetchedData && !isLoading) {
    return (
      <SellerLayout>
        <div className="AddStrategy">
          <div className="error-container">
            <h3>Strategy not found</h3>
            <p>
              {fetchError || "The strategy you're trying to edit doesn't exist or has been removed."}
            </p>
            <div className="error-actions">
              <button
                className="btn btn-primary"
                onClick={() => {
                  setHasFetchedData(false);
                  setFetchError(null);
                }}
              >
                Try Again
              </button>
              <button
                className="btn btn-outline"
                onClick={() => navigate("/seller/my-sports-strategies")}
              >
                Back to Strategies
              </button>
            </div>
          </div>
        </div>
      </SellerLayout>
    );
  }

  return (
    <SellerLayout>
      <div className="AddStrategy">
        {/* Header */}
        <div className="AddStrategy__header">
       
          <p className="AddStrategy__subtitle">Update your strategy details</p>
        </div>

        {/* Main Form */}
        <form className="AddStrategy__form" onSubmit={handleSubmit}>
          {/* Strategy Title */}
          <div className="AddStrategy__field">
            <label className="AddStrategy__label">Strategy Title</label>
            <input
              type="text"
              name="title"
              className="AddStrategy__input"
              placeholder="Add title for Strategy"
              value={formData.title}
              onChange={handleInputChange}
              onBlur={handleFieldBlur}
            />
            {(validationErrors.title || (formSubmitted && !formData.title.trim())) && (
              <div className="AddStrategy__validation-error">
                <p className="AddStrategy__error-message">
                  {validationErrors.title || 'Strategy title is required'}
                </p>
              </div>
            )}
          </div>

          {/* Category */}
          <div className="AddStrategy__field">
            <label className="AddStrategy__label">Category</label>
            <select
              name="category"
              className="AddStrategy__select"
              value={formData.category}
              onChange={handleInputChange}
              onBlur={handleFieldBlur}
            >
              <option value="">Select Category</option>
              <option value="Basketball">Basketball</option>
              <option value="Football">Football</option>
              <option value="Soccer">Soccer</option>
              <option value="Baseball">Baseball</option>
              <option value="Tennis">Tennis</option>
              <option value="Golf">Golf</option>
              <option value="Swimming">Swimming</option>
              <option value="Track and Field">Track and Field</option>
              <option value="Volleyball">Volleyball</option>
              <option value="Hockey">Hockey</option>
              <option value="Other">Other</option>
            </select>
            {(validationErrors.category || (formSubmitted && !formData.category)) && (
              <div className="AddStrategy__validation-error">
                <p className="AddStrategy__error-message">
                  {validationErrors.category || 'Please select a category'}
                </p>
              </div>
            )}
          </div>

          {/* Coach/Seller/Academy Name */}
          <div className="AddStrategy__field">
            <label className="AddStrategy__label">Coach/Seller/Academy Name</label>
            <input
              type="text"
              name="coachName"
              className="AddStrategy__input"
              placeholder="Enter coach, seller, or academy name"
              value={formData.coachName}
              onChange={handleInputChange}
              onBlur={handleFieldBlur}
            />
            {(validationErrors.coachName || (showValidation && !formData.coachName.trim())) && (
              <div className="AddStrategy__validation-error">
                <p className="AddStrategy__error-message">
                  {validationErrors.coachName || 'Coach/Seller/Academy name is required'}
                </p>
              </div>
            )}
          </div>

          {/* Description for Strategy - Rich Text Editor */}
          <div className="AddStrategy__field">
            <label className="AddStrategy__label">Description for Strategy</label>
            <SummernoteEditor
              value={formData.description}
              onChange={(value) => handleSummernoteChange('description', value)}
              placeholder="Enter a detailed description of your strategy..."
              height={200}
              className="AddStrategy__summernote"
              contentKey={`desc-${id}`}
            />
            {(validationErrors.description || (formSubmitted && !formData.description.replace(/<[^>]*>/g, '').trim())) && (
              <div className="AddStrategy__validation-error">
                <p className="AddStrategy__error-message">
                  {validationErrors.description || 'Strategy description is required'}
                </p>
              </div>
            )}
          </div>

          {/* Content Type */}
          <div className="AddStrategy__field">
            <label className="AddStrategy__label">Content Type</label>
            <select
              name="contentType"
              className="AddStrategy__select"
              value={formData.contentType}
              onChange={handleInputChange}
              onBlur={handleFieldBlur}
            >
              <option value="">Select Content Type</option>
              <option value="Video">Video</option>
              <option value="Document">Document</option>
            </select>
            {(validationErrors.contentType || (formSubmitted && !formData.contentType)) && (
              <div className="AddStrategy__validation-error">
                <p className="AddStrategy__error-message">
                  {validationErrors.contentType || 'Please select a content type'}
                </p>
              </div>
            )}
          </div>

          {/* File Upload */}
          <div className="AddStrategy__field">
            <label className="AddStrategy__label">Upload {formData.contentType || 'Video/Document'}</label>
            <div className="AddStrategy__upload">
              <input
                type="file"
                id="file-upload"
                className="AddStrategy__file-input"
                onChange={handleFileUpload}
                accept={getAcceptAttribute(formData.contentType)}
                disabled={isFileUploadDisabled(formData.contentType)}
                style={{ display: "none" }}
              />
              <label
                htmlFor="file-upload"
                className={`AddStrategy__upload-content ${isFileUploadDisabled(formData.contentType) ? 'AddStrategy__upload-content--disabled' : ''}`}
              >
                <FiUpload className="AddStrategy__upload-icon" />
                <p className="AddStrategy__upload-text">
                  {uploadedFile ? uploadedFile.name : (formData.fileUrl ? "Current file uploaded" : getFileUploadPlaceholder(formData.contentType))}
                </p>
              </label>

              {/* File info display */}
              {(uploadedFile || formData.fileUrl) && (
                <div className="AddStrategy__file-info">
                  <p className="AddStrategy__file-name">
                    {uploadedFile ? uploadedFile.name : 'Current file uploaded'}
                  </p>
                  {uploadedFile && (
                    <p className="AddStrategy__file-size">
                      {(uploadedFile.size / 1024 / 1024).toFixed(2)} MB
                    </p>
                  )}
                </div>
              )}
            </div>
            {(validationErrors.fileUpload || (formSubmitted && !formData.fileUrl && !uploadedFile)) && (
              <div className="AddStrategy__validation-error">
                <p className="AddStrategy__error-message">
                  {validationErrors.fileUpload || 'Please upload a video or document file'}
                </p>
              </div>
            )}

            {/* File Preview Section */}
            {formData.fileUrl && (
              <div className="AddStrategy__file-preview">
                <h4 className="AddStrategy__preview-title">Current File Preview</h4>
                {formData.contentType === "Video" && (
                  <div className="AddStrategy__video-preview">
                    <video
                      className="AddStrategy__video-element"
                      controls
                      controlsList="nodownload nofullscreen noremoteplayback"
                      disablePictureInPicture
                      style={{ width: '100%', maxHeight: '300px' }}
                      onError={(e) => {
                        console.error('Video preview error:', e);
                      }}
                    >
                      <source src={getSmartFileUrl(formData.fileUrl)} type="video/mp4" />
                      Your browser does not support the video tag.
                    </video>
                  </div>
                )}
                {formData.contentType === "Document" && (
                  <div className="AddStrategy__document-preview">
                    <DocumentViewer
                      fileUrl={getSmartFileUrl(formData.fileUrl)}
                      fileName={uploadedFile?.name || formData.fileUrl?.split('/').pop() || 'document'}
                      title="Document Preview"
                      className="AddStrategy__document-element"
                      height="300px"
                      showDownload={false}
                    />
                  </div>
                )}

             
              </div>
            )}
          </div>

          {/* About The Coach - Rich Text Editor */}
          <div className="AddStrategy__field">
            <label className="AddStrategy__label">About The Coach</label>
            <SummernoteEditor
              value={formData.aboutCoach}
              onChange={(value) => handleSummernoteChange('aboutCoach', value)}
              placeholder="Share your background, experience, and expertise..."
              height={200}
              className="AddStrategy__summernote"
              contentKey={`coach-${id}`}
            />
            {(validationErrors.aboutCoach || (formSubmitted && !formData.aboutCoach.replace(/<[^>]*>/g, '').trim())) && (
              <div className="AddStrategy__validation-error">
                <p className="AddStrategy__error-message">
                  {validationErrors.aboutCoach || 'About the coach information is required'}
                </p>
              </div>
            )}
          </div>

          {/* Includes Strategic Content - Rich Text Editor */}
          <div className="AddStrategy__field">
            <label className="AddStrategy__label">Includes Strategic Content</label>
            <SummernoteEditor
              value={formData.strategicContent}
              onChange={(value) => handleSummernoteChange('strategicContent', value)}
              placeholder="Describe what strategic content is included..."
              height={200}
              className="AddStrategy__summernote"
              contentKey={`strategic-${id}`}
            />
            {(validationErrors.strategicContent || (formSubmitted && !formData.strategicContent.replace(/<[^>]*>/g, '').trim())) && (
              <div className="AddStrategy__validation-error">
                <p className="AddStrategy__error-message">
                  {validationErrors.strategicContent || 'Strategic content description is required'}
                </p>
              </div>
            )}
          </div>

          {/* Thumbnail Upload */}
          <div className="AddStrategy__field">
            <label className="AddStrategy__label">Thumbnail Image</label>
            <div className="AddStrategy__upload">
              <input
                type="file"
                id="thumbnail-upload"
                className="AddStrategy__file-input"
                accept="image/*"
                onChange={handleThumbnailUpload}
                style={{ display: 'none' }}
              />
              <label htmlFor="thumbnail-upload" className="AddStrategy__upload-content">
                <FiUpload className="AddStrategy__upload-icon" />
                <p className="AddStrategy__upload-text">
                  {formData.thumbnailUrl ? "Thumbnail uploaded" : "Upload thumbnail image"}
                </p>
              </label>

              {/* Thumbnail validation error */}
              {thumbnailError && (
                <div className="AddStrategy__validation-error">
                  <p className="AddStrategy__error-message">{thumbnailError}</p>
                </div>
              )}

              {/* Required field validation error */}
              {(validationErrors.thumbnailUpload || (formSubmitted && !formData.thumbnailUrl)) && (
                <div className="AddStrategy__validation-error">
                  <p className="AddStrategy__error-message">
                    {validationErrors.thumbnailUpload || 'Please upload a thumbnail image'}
                  </p>
                </div>
              )}

              {/* Thumbnail preview */}
              {formData.thumbnailUrl && showThumbnailPreview && (
                <div className="AddStrategy__thumbnail-preview">
                  <img
                    src={getImageUrl(formData.thumbnailUrl)}
                    alt="Thumbnail preview"
                    onError={(e) => {
                      console.error('Thumbnail preview failed:', e);
                      console.error('Failed URL:', getImageUrl(formData.thumbnailUrl));
                      console.error('Original thumbnailUrl:', formData.thumbnailUrl);

                      // Show placeholder image
                      e.target.src = getPlaceholderImage(200, 120, 'Image not found');
                    }}
                    style={{
                      maxWidth: '100%',
                      height: 'auto',
                      borderRadius: 'var(--border-radius)'
                    }}
                  />
                </div>
              )}

              {/* Show placeholder when no thumbnail URL */}
              {!formData.thumbnailUrl && (
                <div className="AddStrategy__thumbnail-preview">
                  <img
                    src={getPlaceholderImage(200, 120, 'No thumbnail')}
                    alt="No thumbnail"
                    style={{
                      maxWidth: '100%',
                      height: 'auto',
                      borderRadius: 'var(--border-radius)',
                      opacity: 0.7
                    }}
                  />
                </div>
              )}
            </div>
          </div>

          {/* Difficulty Level */}
          <div className="AddStrategy__field">
            <label className="AddStrategy__label">Difficulty Level</label>
            <select
              name="difficulty"
              className="AddStrategy__select"
              value={formData.difficulty}
              onChange={handleInputChange}
              onBlur={handleFieldBlur}
            >
              <option value="">Select Difficulty</option>
              <option value="Beginner">Beginner</option>
              <option value="Intermediate">Intermediate</option>
              <option value="Advanced">Advanced</option>
              <option value="Expert">Expert</option>
            </select>
            {(validationErrors.difficulty || (formSubmitted && !formData.difficulty)) && (
              <div className="AddStrategy__validation-error">
                <p className="AddStrategy__error-message">
                  {validationErrors.difficulty || 'Please select a difficulty level'}
                </p>
              </div>
            )}
          </div>

          {/* Language */}
          <div className="AddStrategy__field">
            <label className="AddStrategy__label">Language</label>
            <select
              name="language"
              className="AddStrategy__select"
              value={formData.language}
              onChange={handleInputChange}
              
            >
              <option value="">Select Language</option>
              <option value="English">English</option>
              <option value="Spanish">Spanish</option>
              <option value="French">French</option>
              <option value="German">German</option>
              <option value="Chinese">Chinese</option>
              <option value="Japanese">Japanese</option>
              <option value="Other">Other</option>
            </select>
          </div>

          {/* Tags */}
          <div className="AddStrategy__field">
            <label className="AddStrategy__label">Tags</label>
            <div className="AddStrategy__array-field">
              <div className="AddStrategy__array-input">
                <input
                  type="text"
                  className="AddStrategy__input"
                  placeholder="Add a tag (e.g., basketball, technique, training)..."
                  value={newTag}
                  onChange={(e) => setNewTag(e.target.value)}
                  onKeyPress={(e) => {
                    if (e.key === 'Enter') {
                      e.preventDefault();
                      if (newTag.trim()) {
                        setFormData(prev => ({
                          ...prev,
                          tags: [...prev.tags, newTag.trim()]
                        }));
                        setNewTag("");
                      }
                    }
                  }}
                />
                <button
                  type="button"
                  className="AddStrategy__add-btn"
                  onClick={() => {
                    if (newTag.trim()) {
                      setFormData(prev => ({
                        ...prev,
                        tags: [...prev.tags, newTag.trim()]
                      }));
                      setNewTag("");
                    }
                  }}
                >
                  Add
                </button>
              </div>
              {formData.tags.length > 0 && (
                <div className="AddStrategy__array-items">
                  {formData.tags.map((tag, index) => (
                    <div key={index} className="AddStrategy__array-item">
                      {tag}
                      <button
                        type="button"
                        className="AddStrategy__remove-btn"
                        onClick={() => {
                          setFormData(prev => ({
                            ...prev,
                            tags: prev.tags.filter((_, i) => i !== index)
                          }));
                        }}
                      >
                        X
                      </button>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>

          {/* Sale Type */}
          <div className="AddStrategy__field">
            <label className="AddStrategy__label">Sale Type</label>
            <select
              name="saleType"
              className="AddStrategy__select"
              value={formData.saleType}
              onChange={handleInputChange}
              onBlur={handleFieldBlur}
            >
              <option value="">Select Sale Type</option>
              <option value="Fixed">Fixed Price</option>
              <option value="Auction">Auction</option>
              <option value="Both">Both</option>
            </select>
            {(validationErrors.saleType || (formSubmitted && !formData.saleType)) && (
              <div className="AddStrategy__validation-error">
                <p className="AddStrategy__error-message">
                  {validationErrors.saleType || 'Please select a sale type'}
                </p>
              </div>
            )}
          </div>

          {/* Price */}
          <div className="AddStrategy__field">
            <label className="AddStrategy__label">Price ($)</label>
            <input
              type="number"
              name="price"
              className="AddStrategy__input"
              placeholder="Enter price"
              value={formData.price}
              onChange={handleInputChange}
              onBlur={handleFieldBlur}
              min="0"
              step="0.01"
            />
            {(validationErrors.price || (formSubmitted && (!formData.price || formData.price <= 0))) && (
              <div className="AddStrategy__validation-error">
                <p className="AddStrategy__error-message">
                  {validationErrors.price || 'Please enter a valid price greater than $0'}
                </p>
              </div>
            )}
          </div>

          {/* Allow Custom Requests */}
          <div className="AddStrategy__field">
            <label className="AddStrategy__checkbox-label">
              <input
                type="checkbox"
                name="allowCustomRequests"
                checked={formData.allowCustomRequests}
                onChange={handleInputChange}
                className="AddStrategy__checkbox"
              />
              Allow Custom Requests
            </label>
          </div>

          {/* Custom Request Price (shown only if allowCustomRequests is true) */}
          {formData.allowCustomRequests && (
            <div className="AddStrategy__field">
              <label className="AddStrategy__label">Custom Request Price ($)</label>
              <input
                type="number"
                name="customRequestPrice"
                className="AddStrategy__input"
                placeholder="Enter custom request price"
                value={formData.customRequestPrice}
                onChange={handleInputChange}
                min="0"
                step="0.01"
              />
            </div>
          )}

          {/* Action Buttons */}
          <div className="AddStrategy__actions">
            <button
              type="submit"
              className="btn btn-primary AddStrategy__submit-btn"
              disabled={isSubmitting || isLoading}
            >
              {isSubmitting ? 'Updating...' : 'Update Strategy'}
            </button>
            <button
              type="button"
              className=" btn-outline AddStrategy__reset-btn"
              onClick={() => navigate(`/seller/strategy-details/${id}`)}
              disabled={isSubmitting || isLoading}
            >
              Cancel
            </button>
          </div>
        </form>

        {/* Upload Progress Bar */}
        <UploadProgressBar
          progress={isLoading ? 50 : 0}
          isVisible={showUploadProgress}
          fileName={currentFileName}
          uploadType={currentUploadType}
        />
      </div>
    </SellerLayout>
  );
};

export default EditStrategy;
