const AWS = require('aws-sdk');
const fs = require('fs');
const path = require('path');

// Global S3 instance to avoid multiple initializations
let s3Instance = null;
let credentialsChecked = false;
let hasCredentials = false;

/**
 * Check if all required AWS credentials are available
 * @returns {boolean} - Whether all AWS credentials are present
 */
const hasAWSCredentials = () => {
  if (credentialsChecked) {
    return hasCredentials;
  }

  const requiredCredentials = [
    'AWS_ACCESS_KEY_ID',
    'AWS_SECRET_ACCESS_KEY',
    'AWS_REGION',
    'AWS_BUCKET_NAME'
  ];

  const missingCredentials = requiredCredentials.filter(cred => !process.env[cred]);

  if (missingCredentials.length > 0) {
    console.log(`[Storage] Missing AWS credentials: ${missingCredentials.join(', ')}. Using local storage.`);
    hasCredentials = false;
  } else {
    console.log('[Storage] All AWS credentials found. Using S3 storage.');
    hasCredentials = true;
  }

  credentialsChecked = true;
  return hasCredentials;
};

/**
 * Get configured S3 instance if credentials are available
 * @returns {AWS.S3|null} - S3 instance or null if credentials missing
 */
const getS3Instance = () => {
  if (!hasAWSCredentials()) {
    return null;
  }

  // Return existing instance if already created
  if (s3Instance) {
    return s3Instance;
  }

  try {
    console.log('[Storage] Creating S3 instance with region:', process.env.AWS_REGION);

    // Configure AWS SDK globally
    AWS.config.update({
      accessKeyId: process.env.AWS_ACCESS_KEY_ID,
      secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
      region: process.env.AWS_REGION
    });

    // Create single S3 instance
    s3Instance = new AWS.S3({
      apiVersion: '2006-03-01',
      region: process.env.AWS_REGION,
      signatureVersion: 'v4'
    });

    console.log('[Storage] S3 instance created successfully');

    // Test the instance by checking if it has the required methods
    if (typeof s3Instance.upload === 'function' && typeof s3Instance.getObject === 'function') {
      console.log('[Storage] S3 instance methods verified');
    } else {
      console.error('[Storage] S3 instance missing required methods');
      s3Instance = null;
      return null;
    }

    return s3Instance;
  } catch (error) {
    console.error('[Storage] Error creating S3 instance:', error);
    s3Instance = null;
    return null;
  }
};

/**
 * Determine if S3 storage should be used
 * @returns {boolean} - Whether to use S3 storage
 */
const isUsingS3Storage = () => {
  return hasAWSCredentials();
};

/**
 * Reset S3 instance (useful for testing or credential changes)
 */
const resetS3Instance = () => {
  s3Instance = null;
  credentialsChecked = false;
  hasCredentials = false;
  console.log('[Storage] S3 instance reset');
};

/**
 * Ensure local upload directories exist
 * @param {string} subPath - Optional subdirectory path
 */
const ensureLocalDirectories = (subPath = '') => {
  const baseDir = './uploads';
  const fullPath = subPath ? path.join(baseDir, subPath) : baseDir;

  if (!fs.existsSync(fullPath)) {
    fs.mkdirSync(fullPath, { recursive: true });
    console.log(`[Storage] Created local directory: ${fullPath}`);
  }
};

/**
 * Get the appropriate file URL based on storage type
 * @param {Object} file - Multer file object
 * @param {boolean} generateSignedUrl - Whether to generate signed URL for S3 files
 * @returns {string} - File URL
 */
const getFileUrl = (file, generateSignedUrl = false) => {
  if (!file) {
    throw new Error('File object is required');
  }

  // S3 upload - handle signed URLs or direct URLs
  if (file.location || file.key) {
    if (generateSignedUrl && file.key) {
      return getSignedUrl(file.key);
    }
    return file.location;
  }

  // Local upload - construct URL based on fieldname and filename
  let folder = '';
  if (file.fieldname === 'profileImage') {
    folder = 'profiles/';
  }

  return `/uploads/${folder}${file.filename}`;
};

/**
 * Generate a signed URL for S3 objects
 * @param {string} s3Key - S3 object key
 * @param {number} expiresIn - URL expiration time in seconds (default: 24 hours)
 * @returns {string} - Signed URL
 */
const getSignedUrl = (s3Key, expiresIn = 86400) => {
  const s3 = getS3Instance();
  if (!s3) {
    throw new Error('S3 not configured');
  }

  try {
    const params = {
      Bucket: process.env.AWS_BUCKET_NAME,
      Key: s3Key,
      Expires: expiresIn // 24 hours by default
    };

    const signedUrl = s3.getSignedUrl('getObject', params);
    console.log(`[Storage] Generated signed URL for: ${s3Key}`);
    return signedUrl;
  } catch (error) {
    console.error('[Storage] Error generating signed URL:', error);
    throw error;
  }
};

/**
 * Get a public URL for S3 objects (requires public bucket policy)
 * @param {string} s3Key - S3 object key
 * @returns {string} - Public URL
 */
const getPublicUrl = (s3Key) => {
  const region = process.env.AWS_REGION;
  const bucket = process.env.AWS_BUCKET_NAME;
  return `https://${bucket}.s3.${region}.amazonaws.com/${s3Key}`;
};

/**
 * Detect if a file URL is from S3 storage (but not already a signed URL)
 * @param {string} fileUrl - File URL to check
 * @returns {boolean} - Whether the URL is from S3 and needs signing
 */
const isS3Url = (fileUrl) => {
  if (!fileUrl) return false;

  // Check if it's already a signed URL (contains query parameters like X-Amz-Algorithm)
  if (fileUrl.includes('X-Amz-Algorithm') || fileUrl.includes('X-Amz-Signature')) {
    return false; // Already signed, don't process again
  }

  return fileUrl.includes('amazonaws.com') ||
    fileUrl.includes('s3.') ||
    fileUrl.startsWith('https://') && fileUrl.includes('.s3.');
};

/**
 * Initialize storage directories
 * Creates necessary local directories for file uploads
 */
const initializeStorage = () => {
  console.log('[Storage] Initializing storage system...');

  if (isUsingS3Storage()) {
    console.log('[Storage] Using AWS S3 storage');
    // Test S3 connection
    const s3 = getS3Instance();
    if (s3) {
      console.log('[Storage] S3 instance created successfully');
    }
  } else {
    console.log('[Storage] Using local file storage');
    // Ensure local directories exist
    ensureLocalDirectories();
    ensureLocalDirectories('profile');
    ensureLocalDirectories('previews');
    ensureLocalDirectories('temp');
  }

  // Always ensure profile directory exists (since profile images are always stored locally)
  ensureLocalDirectories('profile');
};

module.exports = {
  hasAWSCredentials,
  getS3Instance,
  isUsingS3Storage,
  ensureLocalDirectories,
  getFileUrl,
  getSignedUrl,
  getPublicUrl,
  isS3Url,
  initializeStorage,
  resetS3Instance
};
