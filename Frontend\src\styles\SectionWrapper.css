.SectionWrapper {
  background-color: var(--white);
  border-radius: var(--border-radius-large);

  margin-bottom: 24px;
  width: 100%;
}
.SectionWrapper .bordrdiv {
  border-bottom: 1px solid #fddcdc;
}
.SectionWrapper .bordrdiv h2 {
  display: inline-flex;
  align-items: center;
  background-color: #fddcdc;
  color: #0a0033;
  padding: 8px 16px;
  border-top-left-radius: 6px;
  border-top-right-radius: 20px;
  border-bottom-left-radius: 0px;
  position: relative;
  font-weight: 600;
  font-family: sans-serif;
  clip-path: polygon(0 0, 95% 0, 100% 100%, 0% 100%);
}

.SectionWrapper .SectionWrapper__title {
  font-size: var(--heading5);
  color: var(--secondary-color);

  font-weight: 600;
}

.SectionWrapper .SectionWrapper__content {
  width: 100%;
}

/* Responsive styles */
@media (max-width: 768px) {
  .SectionWrapper {
    margin-bottom: 16px;
  }

  .SectionWrapper .SectionWrapper__title {
    font-size: var(--heading6);
  }
}
