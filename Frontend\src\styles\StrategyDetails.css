/* StrategyDetails Component Styles */
.StrategyDetails {
  padding: 0;
  background-color: var(--white);
  font-family: "Poppins", sans-serif;
  color: var(--text-color);
}

/* Video/Document Info Header Section */
.StrategyDetails__info-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: var(--heading5);
  border-bottom: 1px solid var(--light-gray);
  margin-bottom: var(--heading4);
}

.StrategyDetails__info-content {
  flex: 1;
}

.StrategyDetails__info-title {
  font-size: var(--heading6);
  color: var(--secondary-color);
  font-weight: 600;
}

.StrategyDetails__strategy-title {
  font-size: var(--basefont);
  color: var(--text-color);
  font-weight: 500;
  margin: 0;
  line-height: 1.4;
}

.StrategyDetails__info-actions {
  display: flex;
  gap: var(--smallfont);
  align-items: center;
}

.StrategyDetails__edit-btn,
.StrategyDetails__delete-btn {
  display: flex;
  align-items: center;
  gap: var(--extrasmallfont);
  padding: var(--extrasmallfont) var(--smallfont);
  border: 1px solid;
  border-radius: var(--border-radius);
  font-size: var(--smallfont);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  background-color: transparent;
}

.StrategyDetails__edit-btn {
  color: #2e8e41;
  border-color: #2e8e41;
}

.StrategyDetails__edit-btn:hover {
  background-color: #2e8e41;
  color: var(--white);
}

.StrategyDetails__delete-btn {
  color: #dc3545;
  border-color: #dc3545;
}

.StrategyDetails__delete-btn:hover {
  background-color: #dc3545;
  color: var(--white);
}

.StrategyDetails__action-icon {
  font-size: var(--basefont);
}

/* Header Section - Using Seller Layout Style */
.StrategyDetails__header {
  margin-bottom: var(--heading4);
  border-bottom: 1px solid #fddcdc;
  padding-bottom: var(--basefont);
}

.StrategyDetails__title-container {
  margin-bottom: var(--smallfont);
}

.StrategyDetails__title {
  display: inline-flex;
  align-items: center;
  background-color: #fddcdc;
  color: #0a0033;
  padding: var(--smallfont) var(--basefont);
  border-top-left-radius: var(--border-radius);
  border-top-right-radius: var(--heading5);
  border-bottom-left-radius: 0;
  position: relative;
  font-weight: 600;
  font-family: sans-serif;
  clip-path: polygon(0 0, 95% 0, 100% 100%, 0% 100%);
  font-size: var(--heading5);
  margin: 0;
  gap: var(--smallfont);
  line-height: 1.3;
}

.StrategyDetails__subtitle {
  font-size: var(--basefont);
  color: var(--dark-gray);
  margin: 0;
  font-weight: 500;
}

/* Main Content */
.StrategyDetails__content {
  display: flex;
  flex-direction: column;
  gap: var(--heading4);
}

/* Media Section */
.StrategyDetails__media-container {
  width: 100%;
  border-radius: var(--border-radius-large);
  overflow: hidden;
  box-shadow: var(--box-shadow);
  background: #000;
}

.StrategyDetails__image-container {
  position: relative;
  width: 100%;
  border-radius: var(--border-radius-large);
  overflow: hidden;
}

.StrategyDetails__image {
  width: 100%;
  height: auto;
  max-height: 450px;
  object-fit: contain;
  display: block;
}

.StrategyDetails__play-overlay {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: rgba(238, 52, 37, 0.9);
  border-radius: 50%;
  width: var(--heading1);
  height: var(--heading1);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;

  height: 70px;
  width: 70px;
}

.StrategyDetails__play-overlay:hover {
  background-color: var(--primary-color);
  transform: translate(-50%, -50%) scale(1.1);
}

.StrategyDetails__play-icon {
  color: var(--white);
  font-size: var(--heading4);
}

.StrategyDetails__overlay-text {
  color: var(--white);
  font-size: var(--extrasmallfont);
  font-weight: 500;
  text-align: center;
}

/* Document Viewer Styles */
.StrategyDetails__document-container,
.StrategyDetails__pdf-container {
  position: relative;
  width: 100%;
  border-radius: var(--border-radius-large);
  overflow: hidden;
  box-shadow: var(--box-shadow);
  background: var(--white);
}

.StrategyDetails__document-viewer,
.StrategyDetails__pdf-viewer {
  position: relative;
  width: 100%;
  height: 500px;
  background-color: var(--white);
  border-radius: var(--border-radius-medium);
  overflow: hidden;
}

.StrategyDetails__pdf-element {
  width: 100%;
  height: 100%;
  border: none;
  background-color: var(--white);
}

.StrategyDetails__pdf-controls {
  position: absolute;
  top: var(--smallfont);
  right: var(--smallfont);
  display: flex;
  gap: var(--extrasmallfont);
  z-index: 10;
}

/* Video Player Styles */
.StrategyDetails__video-wrapper {
  position: relative;
  width: 100%;
  background: #000;
}

.StrategyDetails__preview-indicator {
  position: absolute;
  top: 10px;
  left: 10px;
  background: rgba(0, 0, 0, 0.8);
  color: var(--white);
  padding: 5px 10px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  z-index: 10;
  pointer-events: none;
}

.StrategyDetails__video {
  width: 100%;
  height: auto;
  min-height: 300px;
  max-height: 500px;
  object-fit: contain;
  background: #000;
  /* Prevent fullscreen and ensure video stays in container */
  max-width: 100%;
  display: block;
}

/* Prevent video from going fullscreen */
.StrategyDetails__video::-webkit-media-controls-fullscreen-button {
  display: none !important;
}

.StrategyDetails__video::-moz-media-controls-fullscreen-button {
  display: none !important;
}

.StrategyDetails__video-controls {
  position: absolute;
  top: var(--smallfont);
  right: var(--smallfont);
  display: flex;
  gap: var(--extrasmallfont);
  z-index: 10;
}

.StrategyDetails__control-btn {
  background: rgba(0, 0, 0, 0.7);
  color: var(--white);
  border: none;
  padding: var(--extrasmallfont) var(--smallfont);
  border-radius: var(--border-radius);
  cursor: pointer;
  font-size: var(--smallfont);
  font-weight: 500;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: var(--extrasmallfont);
  backdrop-filter: blur(4px);
}

.StrategyDetails__control-btn:hover {
  background: rgba(0, 0, 0, 0.9);
  transform: translateY(-1px);
}

/* Section Titles */
.StrategyDetails__section-title {
  font-size: var(--heading6);
  color: var(--secondary-color);
  font-weight: 600;
  margin: 0 0 var(--basefont) 0;
 
}

/* Coach Section */
.StrategyDetails__coach-section {
  
  border-radius: var(--border-radius-large);
}

.StrategyDetails__coach-info {
  display: flex;
  flex-direction: column;
  gap: var(--basefont);
}

.StrategyDetails__coach-name {
  font-size: var(--heading6);
  color: var(--text-color);
  font-weight: 600;
  margin: 0 0 var(--extrasmallfont) 0;
}

.StrategyDetails__coach-title {
  font-size: var(--basefont);
  color: var(--dark-gray);
  font-weight: 500;
  margin: 0 0 var(--basefont) 0;
}

.StrategyDetails__coach-description {
  font-size: var(--basefont);
  color: var(--dark-gray);
  line-height: 1.6;
  margin: 0;
}

/* Stats Section */
.StrategyDetails__stats-section {
  background-color: var(--white);

  border-radius: var(--border-radius-large);
}

.StrategyDetails__stats-grid {
  display: grid;

  gap: var(--basefont);
}



.StrategyDetails__stat-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: var(--heading3);
  height: var(--heading3);
  background-color: var(--primary-light-color);
  border-radius: 50%;
  color: var(--btn-color);
  font-size: var(--heading6);
  flex-shrink: 0;
}

.StrategyDetails__stat-content {
  display: flex;
justify-content: space-between;
  gap: var(--extrasmallfont);
}

.StrategyDetails__stat-label
 {
  font-size: var(--basefont);
  color: var(--text-color);
  font-weight: 600;
}
.StrategyDetails__stat-value{
font-size: var(--basefont);
}
.StrategyDetails__stat-label::after {
  content: ":";
   font-size: var(--basefont);
   margin-left: 4px;
}
.StryDetails__stat-valueateg {
font-size: var(--smallfont);
  color: var(--dark-gray);
  font-weight: 500;
}

/* Description Section */
.StrategyDetails__description-section {
  background-color: var(--white);

  border-radius: var(--border-radius-large);
}

.StrategyDetails__description {
  font-size: var(--basefont);
  color: var(--text-color);
  line-height: 1.7;
  margin: 0;
}

/* HTML Content Styling */
.StrategyDetails__description p,
.StrategyDetails__coach-description p,
.StrategyDetails__step-text p {
  margin: 0 0 var(--smallfont) 0;
  line-height: 1.6;
}

.StrategyDetails__description p:last-child,
.StrategyDetails__coach-description p:last-child,
.StrategyDetails__step-text p:last-child {
  margin-bottom: 0;
}

.StrategyDetails__description strong,
.StrategyDetails__coach-description strong,
.StrategyDetails__step-text strong {
  color: var(--btn-color);
  font-weight: 600;
}

.StrategyDetails__description em,
.StrategyDetails__coach-description em,
.StrategyDetails__step-text em {
  font-style: italic;
  color: var(--dark-gray);
}

.StrategyDetails__description ul,
.StrategyDetails__coach-description ul,
.StrategyDetails__step-text ul {
  margin: var(--smallfont) 0;
  padding-left: var(--heading5);
}

.StrategyDetails__description li,
.StrategyDetails__coach-description li,
.StrategyDetails__step-text li {
  margin-bottom: var(--extrasmallfont);
  line-height: 1.5;
}

.StrategyDetails__description ol,
.StrategyDetails__coach-description ol,
.StrategyDetails__step-text ol {
  margin: var(--smallfont) 0;
  padding-left: var(--heading5);
}

.StrategyDetails__description h1,
.StrategyDetails__description h2,
.StrategyDetails__description h3,
.StrategyDetails__description h4,
.StrategyDetails__description h5,
.StrategyDetails__description h6,
.StrategyDetails__coach-description h1,
.StrategyDetails__coach-description h2,
.StrategyDetails__coach-description h3,
.StrategyDetails__coach-description h4,
.StrategyDetails__coach-description h5,
.StrategyDetails__coach-description h6,
.StrategyDetails__step-text h1,
.StrategyDetails__step-text h2,
.StrategyDetails__step-text h3,
.StrategyDetails__step-text h4,
.StrategyDetails__step-text h5,
.StrategyDetails__step-text h6 {
  color: var(--text-color);
  font-weight: 600;
  margin: var(--basefont) 0 var(--smallfont) 0;
  line-height: 1.3;
}

.StrategyDetails__description h1,
.StrategyDetails__coach-description h1,
.StrategyDetails__step-text h1 {
  font-size: var(--heading5);
}

.StrategyDetails__description h2,
.StrategyDetails__coach-description h2,
.StrategyDetails__step-text h2 {
  font-size: var(--heading6);
}

.StrategyDetails__description h3,
.StrategyDetails__coach-description h3,
.StrategyDetails__step-text h3 {
  font-size: var(--basefont);
}

.StrategyDetails__description h4,
.StrategyDetails__description h5,
.StrategyDetails__description h6,
.StrategyDetails__coach-description h4,
.StrategyDetails__coach-description h5,
.StrategyDetails__coach-description h6,
.StrategyDetails__step-text h4,
.StrategyDetails__step-text h5,
.StrategyDetails__step-text h6 {
  font-size: var(--smallfont);
}
.StrategyDetails__stats-and-steps {
  display: grid;

  justify-content: space-between;
  gap: 1rem;
  grid-template-columns: 1fr 1px 1fr;
}
/* Steps Section */
.StrategyDetails__steps-section {
  background-color: var(--white);
 
  border-radius: var(--border-radius-large);
}

.StrategyDetails__steps-list {
  display: flex;
  flex-direction: column;
  gap: var(--basefont);
}

.StrategyDetails__step {
  display: flex;
align-items: center;
  gap: var(--extrasmallfont);
justify-content: space-between;

  transition: all 0.3s ease;
}

.StrategyDetails__step:last-child {
  border-bottom: none;
}

.StrategyDetails__step-label {
  font-size: var(--basefont);
  color: var(--text-color);
  font-weight: 600;
  display: flex;
  align-items: center;

}

.StrategyDetails__step-text {
  font-size: var(--basefont);
  color: var(--text-color);
  font-weight: 400;
  line-height: 1.5;
  text-align: center;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}

/* Loading and Error States */
.loading-container,
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid var(--light-gray);
  border-top: 4px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.loading-container p,
.error-container p {
  color: var(--text-color);
  font-size: var(--basefont);
  margin: 0 0 16px 0;
}

.error-container h3 {
  color: var(--text-color);
  font-size: var(--heading5);
  margin: 0 0 12px 0;
  font-weight: 600;
}

.btn {
  padding: 12px 24px;
  border-radius: var(--border-radius);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
  font-size: var(--basefont);
  text-decoration: none;
  display: inline-block;
}


/* CTA Section */

/* Responsive Design */
@media (max-width: 768px) {
  .StrategyDetails__info-header {
    flex-direction: column;
    gap: var(--basefont);
    align-items: stretch;
  }

  .StrategyDetails__info-actions {
    justify-content: flex-start;
  }

  .StrategyDetails__info-title {
    font-size: var(--basefont);
  }

  .StrategyDetails__strategy-title {
    font-size: var(--smallfont);
  }

  .StrategyDetails__edit-btn,
  .StrategyDetails__delete-btn {
    padding: var(--extrasmallfont) var(--extrasmallfont);
    font-size: var(--extrasmallfont);
  }

  .StrategyDetails__title {
    font-size: var(--heading6);
    padding: var(--extrasmallfont) var(--smallfont);
    gap: var(--extrasmallfont);
  }

  .StrategyDetails__subtitle {
    font-size: var(--smallfont);
  }

  .StrategyDetails__content {
    gap: var(--heading5);
  }

  .StrategyDetails__play-icon {
    font-size: var(--heading5);
  }

  .StrategyDetails__video {
    min-height: 200px;
    max-height: 350px;
  }

  .StrategyDetails__document-viewer,
  .StrategyDetails__pdf-viewer {
    height: 350px;
  }

  .StrategyDetails__video-controls {
    top: var(--extrasmallfont);
    right: var(--extrasmallfont);
    gap: 2px;
  }

  .StrategyDetails__control-btn {
    padding: 4px 8px;
    font-size: var(--extrasmallfont);
  }

  .StrategyDetails__section-title {
    font-size: var(--basefont);
  }

  
 
 


  .StrategyDetails__stats-grid {
    grid-template-columns: 1fr;
    gap: var(--smallfont);
  }


  .StrategyDetails__stat-icon {
    width: var(--heading5);
    height: var(--heading5);
    font-size: var(--basefont);
  }

  .StrategyDetails__coach-name {
    font-size: var(--basefont);
  }

  .StrategyDetails__coach-title,
  .StrategyDetails__coach-description,
  .StrategyDetails__description,
  .StrategyDetails__step-text {
    font-size: var(--smallfont);
  }

  .StrategyDetails__step {
   
    gap: var(--smallfont);
  }
}

@media (max-width: 480px) {
  .StrategyDetails__info-header {
    padding: var(--basefont) 0;
  }

  .StrategyDetails__info-title {
    font-size: var(--smallfont);
  }

  .StrategyDetails__strategy-title {
    font-size: var(--extrasmallfont);
  }

  .StrategyDetails__info-actions {
    gap: var(--extrasmallfont);
  }

  .StrategyDetails__edit-btn,
  .StrategyDetails__delete-btn {
    padding: 4px 8px;
    font-size: var(--extrasmallfont);
  }

  .StrategyDetails__action-icon {
    font-size: var(--smallfont);
  }

  .StrategyDetails__title {
    font-size: var(--basefont);
    padding: var(--extrasmallfont) var(--smallfont);
  }

  .StrategyDetails__subtitle {
    font-size: var(--extrasmallfont);
  }

  .StrategyDetails__content {
    gap: var(--basefont);
  }

  


  .StrategyDetails__section-title {
    font-size: var(--smallfont);
    margin-bottom: var(--smallfont);
  }

  .StrategyDetails__play-overlay {
    width: 55px;
    height: 55px;
  }

  .StrategyDetails__play-icon {
    font-size: var(--heading5);
  }

  .StrategyDetails__video {
    min-height: 180px;
    max-height: 250px;
  }

  .StrategyDetails__document-viewer,
  .StrategyDetails__pdf-viewer {
    height: 250px;
  }

  .StrategyDetails__video-controls {
    top: 4px;
    right: 4px;
    gap: 2px;
  }

  .StrategyDetails__control-btn {
    padding: 2px 6px;
    font-size: 10px;
  }


  .StrategyDetails__stat-icon {
    width: var(--basefont);
    height: var(--basefont);
    font-size: var(--smallfont);
  }

  .StrategyDetails__stat-label,
  .StrategyDetails__stat-value {
    font-size: var(--extrasmallfont);
  }

  .StrategyDetails__coach-name,
  .StrategyDetails__coach-title,
  .StrategyDetails__coach-description,
  .StrategyDetails__description,
  .StrategyDetails__step-text {
    font-size: var(--extrasmallfont);
  }
}
.vertical-line {
  display: flex;
  height: 100%;
  background-color: var(--light-gray);
  align-items: center;
}
