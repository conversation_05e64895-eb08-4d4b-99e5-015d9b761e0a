import { useEffect, useState } from "react";
import { use<PERSON><PERSON><PERSON>, useNavigate } from "react-router-dom";
import { useSelector, useDispatch } from "react-redux";
import { getContent } from "../../redux/slices/contentSlice";
import { createOrder } from "../../redux/slices/orderSlice";
import LoadingSkeleton from "../../components/common/LoadingSkeleton";
import { ErrorDisplay } from "../../components/common/ErrorBoundary";
import StrategyCard from "../../components/common/StrategyCard";
import DocumentViewer from "../../components/common/DocumentViewer";
import { MdKeyboardArrowDown, MdAdd, MdRemove } from "react-icons/md";
import "../../styles/BuyerContentDetail.css";
import "../../styles/ItemDetail.css";
import { IMAGE_BASE_URL, VALIDATION } from "../../utils/constants";
import BidModal from "../../components/common/BidModal";
import RequestCustomTrainingModal from "../../components/common/RequestCustomTrainingModal";
import { toast } from "react-toastify";

// FAQ + Related static data
const faqData = [
  {
    id: 1,
    question: "Is simply dummy text of the printing and typesetting industry?",
    answer: "Order a relaxon online and we will reach out to you to schedule delivery.",
  },
  {
    id: 2,
    question: "What is Lorem Ipsum?",
    answer: "Lorem Ipsum is simply dummy text of the printing and typesetting industry.",
  },
  {
    id: 3,
    question: "What is dummy text?",
    answer: "Dummy text is placeholder text commonly used to demonstrate the visual form.",
  },
  {
    id: 4,
    question: "What is dummy text of the printing?",
    answer: "It is a long established fact that a reader will be distracted by the layout.",
  },
  {
    id: 5,
    question: "What is simply dummy text?",
    answer: "There are many variations of passages of Lorem Ipsum available.",
  },
];

const relatedStrategies = [
  {
    id: 2,
    title: "John Calipari - Early Transition Offensive Concepts",
    coach: "Basketball Coaching",
    price: 24.99,
    image: "https://images.unsplash.com/photo-1574623452334-1e0ac2b3ccb4?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80",
    hasVideo: true,
    type: "buy",
  },
  {
    id: 3,
    title: "WR Fundamentals RPOs - Herman Wiggins",
    coach: "Football Coaching",
    price: 19.99,
    image: "https://images.unsplash.com/photo-1560272564-c83b66b1ad12?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80",
    hasVideo: true,
    type: "buy",
  },
  {
    id: 4,
    title: "Mastering the Ball with Anson Dorrance",
    coach: "Soccer Coaching",
    price: 24.99,
    image: "https://images.unsplash.com/photo-1551698618-1dfe5d97d256?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80",
    hasVideo: true,
    type: "buy",
  },
  {
    id: 5,
    title: "Triplanar Training: A systematic approach to elite speed and explosive...",
    coach: "Fitness Training",
    price: 19.99,
    image: "https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80",
    hasVideo: true,
    type: "buy",
  },
];

// Accordion Component
const FAQAccordion = () => {
  const [activeFAQ, setActiveFAQ] = useState(null);
  const toggleFAQ = (id) => setActiveFAQ(activeFAQ === id ? null : id);

  return (
    <div className="faq-accordion">
      {faqData.map((faq) => (
        <div key={faq.id} className="faq-item">
          <button className={`faq-question ${activeFAQ === faq.id ? "active" : ""}`} onClick={() => toggleFAQ(faq.id)}>
            <span>{faq.question}</span>
            <span className="faq-icon">{activeFAQ === faq.id ? <MdRemove /> : <MdAdd />}</span>
          </button>
          {activeFAQ === faq.id && <div className="faq-answer">{faq.answer}</div>}
        </div>
      ))}
    </div>
  );
};


const ContentDetail = () => {
  const { id } = useParams();
  const dispatch = useDispatch();
  const navigate = useNavigate();

  const [content, setContent] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [activeTab, setActiveTab] = useState("description");
  const [isBidModalOpen, setIsBidModalOpen] = useState(false);
  const [isRequestModalOpen, setIsRequestModalOpen] = useState(false);
  const [showMoreDesc, setShowMoreDesc] = useState(false);
  const [showMoreCoach, setShowMoreCoach] = useState(false);
  const [isCreatingOrder, setIsCreatingOrder] = useState(false);

  // Redux selectors
  const { user } = useSelector((state) => state.auth);
  const { isLoading: orderLoading } = useSelector((state) => state.order);

  useEffect(() => {
    setLoading(true);
    dispatch(getContent(id))
      .unwrap()
      .then((res) => {
        const data = res.data;
        const formatted = {
          ...data,
          coach: data.coachName,
          coachBio: data.aboutCoach,
          fileUrl: data.fileUrl,
          includes: data.strategicContent,
        };
        setContent(formatted);
        setLoading(false);
      })
      .catch((err) => {
        setError(err.message || "Failed to load content details");
        setLoading(false);
      });
  }, [id, dispatch]);

  const handleBuyNow = async () => {
    // Check if user is logged in
    if (!user) {
      toast.error("Please log in to purchase content");
      navigate("/login");
      return;
    }

    // Check if user is a buyer (use effective role for non-admin users)
    const effectiveRole = user.role === 'admin' ? user.role : (user.activeRole || user.role);
    if (effectiveRole !== "buyer" && user.role !== "admin") {
      toast.error("Only buyers can purchase content");
      return;
    }

    // Validate content ID
    if (!id || id === 'undefined') {
      toast.error("Invalid content ID. Please try again.");
      return;
    }

    setIsCreatingOrder(true);

    try {
      // Create order for fixed price content
      const orderData = {
        contentId: id,
        orderType: "Fixed",
      };

      const result = await dispatch(createOrder(orderData)).unwrap();

      // Extract order data from response
      const createdOrder = result.data || result;

      // Validate the created order
      if (!createdOrder || !createdOrder._id) {
        console.error("Order creation response:", result);
        throw new Error("Order creation failed - no order ID returned");
      }

      // Validate order ID format using utility function
      if (!VALIDATION.isValidObjectId(createdOrder._id)) {
        throw new Error("Invalid order ID format returned from server");
      }

      console.log("Order created successfully:", createdOrder._id);

      // Navigate to checkout with order ID
      navigate(`/checkout/${createdOrder._id}`);
    } catch (error) {
      console.error("Error creating order:", error);
      toast.error(error.message || "Failed to create order. Please try again.");
    } finally {
      setIsCreatingOrder(false);
    }
  };

  if (loading) return <LoadingSkeleton type="content-detail" />;
  if (error)
    return (
      <ErrorDisplay
        title="Error Loading Content"
        message={error}
        onRetry={() => window.location.reload()}
      />
    );
  if (!content) return <div className="content-not-found">Content not found</div>;

  return (
    <div className="ItemDetail BuyerContentDetail">
      <div className="ItemDetail__container max-container">
        <div className="ItemDetail__content">
          <div className="ItemDetail__mainContent">
            <h1 className="ItemDetail__title">{content.title}</h1>
            <p className="ItemDetail__coach">By {content.coach}</p>

            <div className="ItemDetail__imageContainer">
              <div className="ItemDetail__contentPreview">
                <h3 className="ItemDetail__previewTitle">Content Preview</h3>
                {content.contentType === "Video" ? (
                  <div className="ItemDetail__videoWrapper">
                    {content.previewUrl ? (
                      <video
                        className="ItemDetail__videoPreview"
                        controls
                        muted
                        onContextMenu={(e) => e.preventDefault()}
                        controlsList="nodownload noremoteplayback"
                        disablePictureInPicture
                        preload="metadata"
                        onLoadedMetadata={(e) => (e.currentTarget.currentTime = 0)}
                        onTimeUpdate={(e) => {
                          if (e.currentTarget.currentTime > 10) e.currentTarget.pause();
                        }}
                      >
                        <source
                          src={`${content.previewUrl.startsWith('/uploads')
                              ? IMAGE_BASE_URL + content.previewUrl
                              : content.previewUrl
                            }`}
                          type="video/mp4"
                        />
                        Your browser does not support the video tag.
                      </video>
                    ) : (
                      <div className="ItemDetail__noPreview">
                        <p>Preview not available. Purchase to access full content.</p>
                      </div>
                    )}
                  </div>
                ) : (
                  <div className="ItemDetail__documentWrapper">
                    {content.previewUrl ? (
                      <div className="ItemDetail__documentPreview">
                        <DocumentViewer
                          fileUrl={content.previewUrl.startsWith('/uploads')
                              ? IMAGE_BASE_URL + content.previewUrl
                              : content.previewUrl}
                          fileName={(() => {
                            // Try to get filename from available URLs
                            // Priority: fileUrl > accessibleFileUrl > previewUrl
                            const fileUrlName = content.fileUrl?.split('/').pop();
                            const accessibleFileUrlName = content.accessibleFileUrl?.split('/').pop();
                            const previewUrlName = content.previewUrl?.split('/').pop();

                            // Use fileUrl if available (user has full access)
                            if (fileUrlName && !fileUrlName.includes('_preview')) {
                              return fileUrlName;
                            }

                            // Use accessibleFileUrl if available
                            if (accessibleFileUrlName && !accessibleFileUrlName.includes('_preview')) {
                              return accessibleFileUrlName;
                            }

                            // For JSON preview files, try to extract original filename
                            if (previewUrlName && previewUrlName.endsWith('.json')) {
                              // Pattern: timestamp-originalname_preview.json
                              // Extract the original filename part
                              const match = previewUrlName.match(/^\d+-(.+)_preview\.json$/);
                              if (match) {
                                // Reconstruct the original filename
                                const originalPart = match[1];
                                // Look for document extensions in the original part
                                if (originalPart.includes('.docx')) return originalPart + '.docx';
                                if (originalPart.includes('.doc')) return originalPart + '.doc';
                                if (originalPart.includes('.xlsx')) return originalPart + '.xlsx';
                                if (originalPart.includes('.xls')) return originalPart + '.xls';
                                if (originalPart.includes('.pptx')) return originalPart + '.pptx';
                                if (originalPart.includes('.ppt')) return originalPart + '.ppt';

                                // Check for Excel patterns in filename
                                if (originalPart.toLowerCase().includes('project') ||
                                    originalPart.toLowerCase().includes('details') ||
                                    originalPart.toLowerCase().includes('sheet') ||
                                    originalPart.toLowerCase().includes('data')) {
                                  return originalPart + '.xlsx';
                                }

                                // Default to .docx for unrecognized patterns
                                return originalPart + '.docx';
                              }
                            }

                            // Use previewUrl and clean up the name
                            if (previewUrlName) {
                              // Remove _preview suffix if present
                              return previewUrlName.replace('_preview', '');
                            }

                            return 'document.docx';
                          })()}
                          title="Document Preview"
                          className="ItemDetail__documentPreview"
                          height="400px"
                          showDownload={false}
                        />
                      </div>
                    ) : (
                      <div className="ItemDetail__noPreview">
                        <p>Preview not available. Purchase to access full content.</p>
                      </div>
                    )}
                  </div>
                )}
              </div>
            </div>

            <div className="ItemDetail__tabs">
              <div className="ItemDetail__tabButtons">
                {["description", "coach", "faqs"].map((tab) => (
                  <button
                    key={tab}
                    className={`ItemDetail__tabButton ${activeTab === tab ? "ItemDetail__tabButton--active" : ""}`}
                    onClick={() => setActiveTab(tab)}
                  >
                    {tab === "description" ? "Description" : tab === "coach" ? "The Coach" : "FAQs"}
                  </button>
                ))}
              </div>
              <div className="ItemDetail__tabContent">
                {activeTab === "description" && (
                  <div className="ItemDetail__tabPanel">
                    <div
                      className={`ItemDetail__description ${showMoreDesc ? "expanded" : "collapsed"}`}
                      dangerouslySetInnerHTML={{ __html: content.description }}
                    />
                    <div className="seemoreContainer">
                      <button className="ItemDetail__seeMore" onClick={() => setShowMoreDesc(!showMoreDesc)}>
                        {showMoreDesc ? "See Less" : "See More"} <MdKeyboardArrowDown />
                      </button>
                    </div>
                  </div>
                )}
                {activeTab === "coach" && (
                  <div className="ItemDetail__tabPanel">
                    <div
                      className={`ItemDetail__description ${showMoreCoach ? "expanded" : "collapsed"}`}
                      dangerouslySetInnerHTML={{
                        __html: content.coachBio || "<p>Coach info not available</p>",
                      }}
                    />
                    <div className="seemoreContainer">
                      <button className="ItemDetail__seeMore" onClick={() => setShowMoreCoach(!showMoreCoach)}>
                        {showMoreCoach ? "See Less" : "See More"} <MdKeyboardArrowDown />
                      </button>
                    </div>
                  </div>
                )}
                {activeTab === "faqs" && (
                  <div className="ItemDetail__tabPanel">
                    <FAQAccordion />
                  </div>
                )}
              </div>
            </div>
          </div>

          <aside className="ItemDetail__sidebar">
            <div className="ItemDetail__priceBox">
              <div className="ItemDetail__price">
                <p>Price</p>${content.price?.toFixed(2) || "N/A"}
              </div>
              {/* Always show Buy Now button for fixed price content */}
              {(content.saleType === "Fixed" || content.saleType === "Direct") && (
                <button
                  className="ItemDetail__buyButton btn-primary"
                  onClick={handleBuyNow}
                  disabled={isCreatingOrder || orderLoading}
                >
                  {isCreatingOrder || orderLoading ? "Creating Order..." : "Buy Now"}
                </button>
              )}

              {/* Show Bid Offer button for auction content or if bidding is enabled */}
              {(content.saleType === "Bid" || content.saleType === "Auction" || content.allowBidding) && (
                <button
                  className="ItemDetail__bidButton btn-secondary"
                  onClick={() => setIsBidModalOpen(true)}
                  style={{ marginTop: "8px" }}
                >
                  Bid Offer
                </button>
              )}
              {content.allowCustomRequests && (
                <button className="ItemDetail__Request_Custom_Training" onClick={() => setIsRequestModalOpen(true)}>
                  Request Custom Training
                </button>
              )}
            </div>

            <div className="ItemDetail__contentIncludes">
              <h3 className="ItemDetail__sidebarTitle">Strategic Content</h3>
              <div className="ItemDetail__includesList">
                {content.includes ? (
                  <div
                    className="ItemDetail__strategicContent"
                    dangerouslySetInnerHTML={{ __html: content.includes }}
                  />
                ) : (
                  <p className="ItemDetail__noContent">No strategic content available</p>
                )}
              </div>
            </div>

            <div className="ItemDetail__contentInfo">
              <h3 className="ItemDetail__sidebarTitle">Strategic Content Info</h3>
              <div className="ItemDetail__infoList">
                <div className="ItemDetail__infoItem">
                  <span className="ItemDetail__infoLabel">Category:</span>
                  <span className="ItemDetail__infoValue">{content.category}</span>
                </div>
                {content.bookings && (
                  <div className="ItemDetail__infoItem">
                    <span className="ItemDetail__infoLabel">Bookings:</span>
                    <span className="ItemDetail__infoValue">{content.bookings}</span>
                  </div>
                )}
                {content.duration && (
                  <div className="ItemDetail__infoItem">
                    <span className="ItemDetail__infoLabel">Duration:</span>
                    <span className="ItemDetail__infoValue">{content.duration}</span>
                  </div>
                )}
                <div className="ItemDetail__infoItem">
                  <span className="ItemDetail__infoLabel">Type:</span>
                  <span className="ItemDetail__infoValue">{content.contentType || "Buy Now Content"}</span>
                </div>
              </div>
            </div>
          </aside>
        </div>
      </div>

      {/* Related */}
      <section className="ItemDetail__relatedSection">
        <div className="max-container">
          <div className="flex items-center justify-between">
            <h2 className="ItemDetail__relatedTitle">Related Sports Strategies Students Are Learning</h2>
            <a href="#" className="ItemDetail__learnMoreLink">Learn More Contents</a>
          </div>
          <div className="ItemDetail__relatedGrid">
            {relatedStrategies.map((strategy) => (
              <StrategyCard key={strategy.id} {...strategy} />
            ))}
          </div>
        </div>
      </section>

      {/* Modals */}
      {content.saleType === "Bid" && (
        <BidModal isOpen={isBidModalOpen} onClose={() => setIsBidModalOpen(false)} strategy={content} />
      )}
      {content.allowCustomRequests && (
        <RequestCustomTrainingModal isOpen={isRequestModalOpen} onClose={() => setIsRequestModalOpen(false)} strategy={content} />
      )}
    </div>
  );
};

export default ContentDetail;



