const mongoose = require('mongoose');

const TempUserSchema = new mongoose.Schema(
  {
    firstName: {
      type: String,
      required: [true, 'Please add a first name'],
      trim: true,
      maxlength: [50, 'First name cannot be more than 50 characters']
    },
    lastName: {
      type: String,
      required: [true, 'Please add a last name'],
      trim: true,
      maxlength: [50, 'Last name cannot be more than 50 characters']
    },
    email: {
      type: String,
      required: [true, 'Please add an email'],
      unique: true,
      match: [
        /^\w+([\.-]?\w+)*@\w+([\.-]?\w+)*(\.\w{2,3})+$/,
        'Please add a valid email'
      ]
    },
    mobile: {
      type: String,
      required: [true, 'Please add a mobile number'],
      maxlength: [20, 'Mobile number cannot be longer than 20 characters']
    },
    role: {
      type: String,
      enum: ['buyer', 'seller', 'admin'],
      default: 'buyer'
    },
    // Active role for dual buyer-seller users (admin users don't have activeRole)
    activeRole: {
      type: String,
      enum: ['buyer', 'seller'],
      default: function() {
        // Set activeRole to match role for non-admin users
        return this.role === 'admin' ? undefined : this.role;
      },
    },
    otpCode: String,
    otpExpire: Date,
    createdAt: {
      type: Date,
      default: Date.now,
      expires: 3600 // Document expires after 1 hour
    }
  },
  {
    toJSON: { virtuals: true },
    toObject: { virtuals: true }
  }
);

// Generate OTP
TempUserSchema.methods.generateOTP = function() {
  // Generate a 6-digit OTP
  const otp = Math.floor(100000 + Math.random() * 900000).toString();

  // Set OTP and expiration (10 minutes)
  this.otpCode = otp;
  this.otpExpire = Date.now() + 10 * 60 * 1000;

  return otp;
};

module.exports = mongoose.model('TempUser', TempUserSchema);
