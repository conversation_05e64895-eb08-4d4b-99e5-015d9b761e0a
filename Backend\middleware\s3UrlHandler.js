const { getSignedUrl, isS3Url, isUsingS3Storage } = require('../utils/storageHelper');

/**
 * Middleware to automatically convert S3 URLs to signed URLs in API responses
 * This ensures that S3 files are accessible even if the bucket is private
 */
const convertS3UrlsToSigned = (req, res, next) => {
  // Only apply this middleware if we're using S3 storage
  if (!isUsingS3Storage()) {
    return next();
  }

  // Store the original json method
  const originalJson = res.json;

  // Override the json method
  res.json = function (data) {
    try {
      // Convert S3 URLs to signed URLs in the response data
      const processedData = processS3Urls(data);
      return originalJson.call(this, processedData);
    } catch (error) {
      console.error('[S3UrlHandler] Error processing S3 URLs:', error);
      // If there's an error, return the original data
      return originalJson.call(this, data);
    }
  };

  next();
};

/**
 * Recursively process an object to convert S3 URLs to signed URLs
 * @param {any} obj - The object to process
 * @param {Set} visited - Set to track visited objects to prevent infinite recursion
 * @param {number} depth - Current recursion depth
 * @param {number} maxDepth - Maximum allowed recursion depth
 * @returns {any} - The processed object with signed URLs
 */
const processS3Urls = (obj, visited = new Set(), depth = 0, maxDepth = 5) => {
  if (obj === null || obj === undefined) {
    return obj;
  }

  // Prevent infinite recursion by limiting depth
  if (depth > maxDepth) {
    console.warn(`[S3UrlHandler] Maximum recursion depth (${maxDepth}) reached, stopping processing`);
    return obj;
  }

  // Skip Mongoose documents and other complex objects that might have circular references
  if (typeof obj === 'object' && obj.constructor && obj.constructor.name !== 'Object' && obj.constructor.name !== 'Array') {
    // This is likely a Mongoose document or other complex object
    // Convert to plain object first to avoid circular references
    try {
      if (typeof obj.toJSON === 'function') {
        obj = obj.toJSON();
      } else if (typeof obj.toObject === 'function') {
        obj = obj.toObject();
      } else {
        // For other complex objects, just return as-is to avoid issues
        return obj;
      }
    } catch (error) {
      console.warn(`[S3UrlHandler] Could not convert object to plain object:`, error.message);
      return obj;
    }
  }

  // Prevent infinite recursion by tracking visited objects
  if (typeof obj === 'object' && visited.has(obj)) {
    return obj;
  }

  // Handle arrays
  if (Array.isArray(obj)) {
    visited.add(obj);
    const result = obj.map(item => processS3Urls(item, visited, depth + 1, maxDepth));
    visited.delete(obj);
    return result;
  }

  // Handle plain objects only
  if (typeof obj === 'object' && obj.constructor === Object) {
    visited.add(obj);
    const processed = {};

    for (const [key, value] of Object.entries(obj)) {
      if (typeof value === 'string' && isS3Url(value)) {
        // Convert S3 URL to signed URL
        try {
          const s3Key = extractS3Key(value);
          if (s3Key) {
            processed[key] = getSignedUrl(s3Key, 86400); // 24 hours
            console.log(`[S3UrlHandler] Converted ${key}: ${s3Key} to signed URL`);
          } else {
            processed[key] = value; // Keep original if can't extract key
          }
        } catch (error) {
          console.error(`[S3UrlHandler] Error converting URL for ${key}:`, error);
          processed[key] = value; // Keep original on error
        }
      } else {
        // Recursively process nested objects/arrays
        processed[key] = processS3Urls(value, visited, depth + 1, maxDepth);
      }
    }

    visited.delete(obj);
    return processed;
  }

  // Return primitive values and non-plain objects as-is
  return obj;
};

/**
 * Extract S3 key from S3 URL
 * @param {string} s3Url - The S3 URL
 * @returns {string|null} - The S3 key or null if not found
 */
const extractS3Key = (s3Url) => {
  try {
    // Handle different S3 URL formats:
    // https://bucket.s3.region.amazonaws.com/key
    // https://s3.region.amazonaws.com/bucket/key
    // https://bucket.s3.amazonaws.com/key

    const url = new URL(s3Url);
    const pathParts = url.pathname.split('/').filter(part => part.length > 0);

    if (url.hostname.includes('.s3.') || url.hostname.includes('.s3-')) {
      // Format: https://bucket.s3.region.amazonaws.com/key
      return pathParts.join('/');
    } else if (url.hostname.startsWith('s3.')) {
      // Format: https://s3.region.amazonaws.com/bucket/key
      return pathParts.slice(1).join('/'); // Remove bucket name
    }

    return null;
  } catch (error) {
    console.error('[S3UrlHandler] Error extracting S3 key:', error);
    return null;
  }
};

/**
 * Middleware specifically for content routes that need signed URLs
 */
const convertContentS3Urls = (req, res, next) => {
  // Apply the general S3 URL conversion
  convertS3UrlsToSigned(req, res, next);
};

module.exports = {
  convertS3UrlsToSigned,
  convertContentS3Urls,
  processS3Urls,
  extractS3Key
};
