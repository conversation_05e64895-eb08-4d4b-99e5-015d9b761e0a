import React, { useState, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import SellerLayout from "../../components/seller/SellerLayout";
import Table from "../../components/common/Table";
import { getSellerContent, toggleContentStatus, deleteContent } from "../../redux/slices/contentSlice";
import { toast } from "react-toastify";
import "../../styles/SellerMySportsStrategies.css";
import { SlEye } from "react-icons/sl";
import { MdEdit, MdDelete } from "react-icons/md";
import { getSmartFileUrl } from "../../utils/constants";
import ConfirmationModal from "../../components/common/ConfirmationModal";

const SellerMySportsStrategies = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { sellerContent, isLoading, error } = useSelector((state) => state.content);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [selectedStrategy, setSelectedStrategy] = useState(null);

  // Fetch seller content on component mount
  useEffect(() => {
    dispatch(getSellerContent());
  }, [dispatch]);

  // Handle error display
  useEffect(() => {
    if (error) {
      toast.error(error.message || "Failed to load strategies");
    }
  }, [error]);

  const handleToggleStatus = async (id) => {
    try {
      await dispatch(toggleContentStatus(id)).unwrap();
      toast.success("Strategy status updated successfully");
    } catch (error) {
      toast.error(error.message || "Failed to update strategy status");
    }
  };

  const handleViewDetails = (id) => {
    navigate(`/seller/strategy-details/${id}`);
  };

  const handleEdit = (id) => {
    navigate(`/seller/strategy-details/${id}/edit`);
  };

  const handleDeleteClick = (strategy) => {
    setSelectedStrategy(strategy);
    setShowDeleteModal(true);
  };

  const handleDeleteConfirm = async () => {
    if (!selectedStrategy) return;

    try {
      await dispatch(deleteContent(selectedStrategy._id)).unwrap();
      toast.success("Strategy deleted successfully");
      setShowDeleteModal(false);
      setSelectedStrategy(null);
      // Refresh the content list
      dispatch(getSellerContent());
    } catch (error) {
      toast.error(error.message || "Failed to delete strategy");
    }
  };

  const handleDeleteCancel = () => {
    setShowDeleteModal(false);
    setSelectedStrategy(null);
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  const formatPrice = (price) => {
    return typeof price === "number" ? `$${price.toFixed(2)}` : price || "$0.00";
  };

  const columns = [
    { key: "no", label: "No.", className: "no" },
    {
      key: "content",
      label: "Videos/Documents",
      render: (item) => (
        <div className="video-doc">
          <div className="video-thumbnail">
            {item.thumbnailUrl ? (
              <img src={getSmartFileUrl(item.thumbnailUrl)} alt={item.title} />
            ) : (
              <div className="placeholder-thumb">
                {item.contentType === "Video" ? "📹" : "📄"}
              </div>
            )}
          </div>
          <span className="video-title">{item.title}</span>
        </div>
      ),
    },
    {
      key: "date",
      label: "Date",
      render: (item) => formatDate(item.createdAt)
    },
    {
      key: "price",
      label: "Price",
      render: (item) => formatPrice(item.price)
    },
    {
      key: "status",
      label: "Status",
      render: (item) => (
        <label className="switch">
          <input
            type="checkbox"
            checked={item.isActive === 1}
            onChange={() => handleToggleStatus(item._id)}
            disabled={isLoading}
          />
          <span className="slider round"></span>
        </label>
      ),
    },
    {
      key: "action",
      label: "Action",
      render: (item) => (
        <div className="action-icon-container">
          <SlEye
            className="action-icon eyeicon"
            onClick={() => handleViewDetails(item._id)}
            title="View Details"
          />
          <MdEdit
            className=" edit-icon"
            onClick={() => handleEdit(item._id)}
            title="Edit Strategy"
          />
          <MdDelete
            className=" delete-icon"
            onClick={() => handleDeleteClick(item)}
            title="Delete Strategy"
          />
        </div>
      ),
    },
  ];

  const formatData = (strategies) => {
    return strategies.map((item, index) => ({
      ...item,
      no: index + 1,
    }));
  };

  if (isLoading) {
    return (
      <SellerLayout>
        <div className="video-status-container">
          <div className="loading-container">
            <div className="loading-spinner"></div>
            <p>Loading strategies...</p>
          </div>
        </div>
      </SellerLayout>
    );
  }

  return (
    <SellerLayout>
      <div className="video-status-container">
        {sellerContent.length === 0 ? (
          <div className="empty-state">
            <h3>No strategies found</h3>
            <p>You haven't created any strategies yet. Click "Add New Strategy" to get started.</p>
          </div>
        ) : (
          <Table
            columns={columns}
            data={formatData(sellerContent)}
            className="video-table"
          />
        )}
      </div>

      {/* Delete Confirmation Modal */}
      <ConfirmationModal
        isOpen={showDeleteModal}
        onConfirm={handleDeleteConfirm}
        onClose={handleDeleteCancel}
        title="Delete Strategy"
        message={`Are you sure you want to delete "${selectedStrategy?.title}"? This action cannot be undone.`}
        confirmText="Delete"
        cancelText="Cancel"
        type="danger"
      />
    </SellerLayout>
  );
};

export default SellerMySportsStrategies;
