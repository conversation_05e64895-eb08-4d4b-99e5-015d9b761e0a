/* Media Viewer Styles */
.media-viewer-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.95);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: var(--z-index-modal);
  padding: var(--basefont);
}

.media-viewer {
  background: var(--white);
  border-radius: var(--border-radius-large);
  max-width: 90vw;
  max-height: 90vh;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  box-shadow: var(--box-shadow-dark);
  animation: modalSlideIn 0.3s ease-out;
  overflow: hidden;
}

.media-viewer--fullscreen {
  max-width: 100vw;
  max-height: 100vh;
  border-radius: 0;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Header */
.media-viewer__header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--basefont) var(--heading5);
  border-bottom: 1px solid var(--light-gray);
  background: var(--white);
  flex-shrink: 0;
}

.media-viewer__title {
  display: flex;
  flex-direction: column;
  gap: var(--extrasmallfont);
}

.media-viewer__title h3 {
  margin: 0;
  font-size: var(--basefont);
  font-weight: 600;
  color: var(--text-color);
  line-height: 1.2;
}

.media-viewer__type {
  font-size: var(--extrasmallfont);
  color: var(--dark-gray);
  font-weight: 500;
  background: var(--light-gray);
  padding: 2px 6px;
  border-radius: 4px;
  align-self: flex-start;
}

.media-viewer__controls {
  display: flex;
  gap: var(--extrasmallfont);
  align-items: center;
}

.media-viewer__control-btn {
  background: none;
  border: none;
  padding: var(--extrasmallfont);
  border-radius: var(--border-radius);
  cursor: pointer;
  color: var(--dark-gray);
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
}

.media-viewer__control-btn:hover {
  background: var(--light-gray);
  color: var(--text-color);
}

.media-viewer__close-btn:hover {
  background: #dc3545;
  color: var(--white);
}

/* Content */
.media-viewer__content {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--basefont);
  background: #000;
  overflow: hidden;
}

/* Video */
.media-viewer__video {
  width: 100%;
  height: 100%;
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

/* Audio */
.media-viewer__audio-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: var(--heading5);
  background: var(--white);
  padding: var(--heading4);
  border-radius: var(--border-radius-large);
  text-align: center;
  min-width: 400px;
}

.media-viewer__audio-info h3 {
  margin: 0 0 var(--extrasmallfont) 0;
  font-size: var(--heading6);
  color: var(--text-color);
}

.media-viewer__audio-info p {
  margin: 0;
  color: var(--dark-gray);
  font-size: var(--basefont);
}

.media-viewer__audio {
  width: 100%;
  max-width: 400px;
}

/* Image */
.media-viewer__image {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  border-radius: var(--border-radius);
}

/* PDF */
.media-viewer__pdf {
  width: 100%;
  height: 100%;
  border: none;
  border-radius: var(--border-radius);
}

/* Document */
.media-viewer__document {
  width: 100%;
  height: 100%;
  border: none;
  border-radius: var(--border-radius);
}

/* Unsupported */
.media-viewer__unsupported {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  background: var(--white);
}

.media-viewer__unsupported-content {
  text-align: center;
  padding: var(--heading4);
  max-width: 400px;
}

.media-viewer__unsupported-content h3 {
  margin: 0 0 var(--basefont) 0;
  font-size: var(--heading6);
  color: var(--text-color);
}

.media-viewer__unsupported-content p {
  margin: 0 0 var(--smallfont) 0;
  color: var(--dark-gray);
  font-size: var(--basefont);
  line-height: 1.5;
}

.media-viewer__download-btn {
  display: inline-flex;
  align-items: center;
  gap: var(--extrasmallfont);
  padding: var(--smallfont) var(--basefont);
  background: var(--btn-color);
  color: var(--white);
  border: none;
  border-radius: var(--border-radius);
  font-size: var(--basefont);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-top: var(--basefont);
}

.media-viewer__download-btn:hover {
  background: var(--primary-color);
}

/* Footer */
.media-viewer__footer {
  padding: var(--smallfont) var(--heading5);
  border-top: 1px solid var(--light-gray);
  background: var(--white);
  flex-shrink: 0;
}

.media-viewer__info {
  display: flex;
  gap: var(--smallfont);
  font-size: var(--smallfont);
  color: var(--dark-gray);
  align-items: center;
}

.media-viewer__info a {
  color: var(--btn-color);
  text-decoration: none;
}

.media-viewer__info a:hover {
  text-decoration: underline;
}

/* Responsive Design */
@media (max-width: 768px) {
  .media-viewer-overlay {
    padding: 0;
  }

  .media-viewer {
    max-width: 100vw;
    max-height: 100vh;
    border-radius: 0;
  }

  .media-viewer__header {
    padding: var(--smallfont) var(--basefont);
  }

  .media-viewer__title h3 {
    font-size: var(--smallfont);
  }

  .media-viewer__content {
    padding: var(--smallfont);
  }

  .media-viewer__audio-container {
    min-width: auto;
    width: 100%;
    padding: var(--basefont);
  }

  .media-viewer__footer {
    padding: var(--smallfont) var(--basefont);
  }

  .media-viewer__info {
    flex-direction: column;
    gap: var(--extrasmallfont);
    align-items: flex-start;
  }
}

@media (max-width: 480px) {
  .media-viewer__controls {
    gap: 2px;
  }

  .media-viewer__control-btn {
    width: 28px;
    height: 28px;
    font-size: var(--smallfont);
  }

  .media-viewer__title h3 {
    font-size: var(--extrasmallfont);
  }

  .media-viewer__type {
    font-size: 10px;
  }
}
