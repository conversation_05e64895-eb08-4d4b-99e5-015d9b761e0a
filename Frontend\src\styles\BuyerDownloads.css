.BuyerDownloads {
  display: flex;
  flex-direction: column;
  width: 100%;
}

/* Table Styles - Following BuyerAccountDashboard pattern */
.BuyerDownloads .table {
  width: 100%;
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius);
  overflow: hidden;
}

.BuyerDownloads .table-header {
  display: grid;
  grid-template-columns: 0.5fr 1fr 3fr 1.5fr 1fr 1fr 1fr;
  background-color: var(--bg-gray);
  padding: var(--smallfont) var(--basefont);
  font-weight: 600;
  color: var(--secondary-color);
  border-bottom: 1px solid var(--light-gray);
}

.BuyerDownloads .table-row {
  display: grid;
  grid-template-columns: 0.5fr 1fr 3fr 1.5fr 1fr 1fr 1fr;
  padding: var(--smallfont) var(--basefont);
  border-bottom: 1px solid var(--light-gray);
  align-items: center;
}

.BuyerDownloads .table-row:last-child {
  border-bottom: none;
}

.BuyerDownloads .table-cell {
  padding: 0 var(--extrasmallfont);
  font-size: var(--smallfont);
  text-align: center; /* Center all content as requested */
}

.BuyerDownloads .content-item {
  display: flex;
  align-items: center;
  gap: var(--smallfont);
 
}

.BuyerDownloads .content-image {
  width: 50px;
  height: 50px;
  border-radius: var(--border-radius);
  overflow: hidden;
  flex-shrink: 0;
}

.BuyerDownloads .content-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.BuyerDownloads .content-info {
  display: flex;
  flex-direction: column;
  text-align: left; /* Keep text left-aligned within the centered container */
}

.BuyerDownloads .content-title {
  font-weight: 500;
  color: var(--text-color);
  margin-bottom: 2px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 200px;
}

.BuyerDownloads .content-coach {
  font-size: var(--extrasmallfont);
  color: var(--dark-gray);
}

.BuyerDownloads .content-type {
  font-size: var(--extrasmallfont);
  color: var(--dark-gray);
  margin-top: 2px;
}

.BuyerDownloads .status-badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: var(--border-radius);
  font-size: var(--extrasmallfont);
  font-weight: 500;
  text-align: center;
}

.BuyerDownloads .status-badge.downloaded {
  background-color: rgba(46, 204, 113, 0.1);
  color: #2ecc71;
}

.BuyerDownloads .status-badge.pending {
  background-color: rgba(243, 156, 18, 0.1);
  color: #f39c12;
}

/* Action buttons styling */
.BuyerDownloads .action-buttons {
  display: flex;
  gap: 0.5rem;
  align-items: center;
  justify-content: center;
}

.BuyerDownloads .action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 14px;
}

.BuyerDownloads .download-btn {
  background-color: #28a745;
  color: white;
}

.BuyerDownloads .download-btn:hover:not(:disabled) {
  background-color: #218838;
  transform: translateY(-1px);
}

.BuyerDownloads .download-btn:disabled {
  background-color: #6c757d;
  cursor: not-allowed;
}

.BuyerDownloads .view-btn {
  background-color: #007bff;
  color: white;
}

.BuyerDownloads .view-btn:hover {
  background-color: #0056b3;
  transform: translateY(-1px);
}

/* Spinner for loading state */
.BuyerDownloads .spinner {
  width: 14px;
  height: 14px;
  border: 2px solid #ffffff;
  border-top: 2px solid transparent;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.BuyerDownloads__empty {
  text-align: center;
  padding: var(--heading4);
  color: var(--dark-gray);
}

.BuyerDownloads__empty p {
  font-size: var(--basefont);
}

/* Responsive styles */
@media (max-width: 992px) {
  .BuyerDownloads .table-header,
  .BuyerDownloads .table-row {
    grid-template-columns: 0.5fr 1fr 2fr 1.5fr 1fr 1fr 1fr;
  }

  .BuyerDownloads .content-title {
    max-width: 150px;
  }
}

@media (max-width: 768px) {
  .BuyerDownloads .table {
    overflow-x: auto;
  }

  .BuyerDownloads .table-header,
  .BuyerDownloads .table-row {
    min-width: 700px;
  }
}
