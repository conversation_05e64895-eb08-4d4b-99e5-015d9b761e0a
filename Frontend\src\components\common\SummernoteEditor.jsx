// import React, { useEffect, useRef } from 'react';

// // Import Summernote and its dependencies
// import 'summernote/dist/summernote-lite.css';
// import 'bootstrap/dist/css/bootstrap.css';

// const SummernoteEditor = ({
//   value = '',
//   onChange,
//   placeholder = 'Enter text here...',
//   height = 350,
//   className = '',
//   disabled = false,
//   options = {}
// }) => {
//   const editorRef = useRef(null);
//   const summernoteRef = useRef(null);

//   useEffect(() => {
//     // Dynamically import jQuery and Summernote to avoid SSR issues
//     const initializeSummernote = async () => {
//       try {
//         // Import jQuery
//         const $ = (await import('jquery')).default;

//         // Make jQuery available globally for Summernote
//         window.jQuery = $;
//         window.$ = $;

//         // Import Summernote
//         await import('summernote/dist/summernote-lite.js');

//         // Initialize Summernote
//         if (editorRef.current && !summernoteRef.current) {
//           const defaultOptions = {
//             height: height,
//             placeholder: placeholder,
//             toolbar: [
//               ['style', ['style']],
//               ['font', ['bold', 'underline', 'clear']],
//               ['fontname', ['fontname']],
//               ['para', ['ul', 'ol', 'paragraph']],
//               ['table', ['table']],
//               ['insert', ['link', 'picture', 'video']],
//               ['view', ['fullscreen', 'codeview']]
//             ],
//             callbacks: {
//               onChange: function (contents) {
//                 if (onChange) {
//                   onChange(contents);
//                 }
//               }
//             },
//             ...options
//           };

//           $(editorRef.current).summernote(defaultOptions);
//           summernoteRef.current = $(editorRef.current);

//           // Set initial value
//           if (value) {
//             $(editorRef.current).summernote('code', value);
//           }

//           // Handle disabled state
//           if (disabled) {
//             $(editorRef.current).summernote('disable');
//           }
//         }
//       } catch (error) {
//         console.error('Failed to initialize Summernote:', error);
//       }
//     };

//     initializeSummernote();

//     // Cleanup function
//     return () => {
//       if (summernoteRef.current) {
//         try {
//           summernoteRef.current.summernote('destroy');
//           summernoteRef.current = null;
//         } catch (error) {
//           console.error('Error destroying Summernote:', error);
//         }
//       }
//     };
//   }, []);

//   // Update content when value prop changes
//   useEffect(() => {
//     if (summernoteRef.current && value !== undefined) {
//       const currentContent = summernoteRef.current.summernote('code');
//       if (currentContent !== value) {
//         summernoteRef.current.summernote('code', value);
//       }
//     }
//   }, [value]);

//   // Update disabled state
//   useEffect(() => {
//     if (summernoteRef.current) {
//       if (disabled) {
//         summernoteRef.current.summernote('disable');
//       } else {
//         summernoteRef.current.summernote('enable');
//       }
//     }
//   }, [disabled]);

//   return (
//     <div className={`summernote-wrapper ${className}`}>
//       <div ref={editorRef}></div>
//     </div>
//   );
// };

// export default SummernoteEditor;



import React, { useEffect, useRef, useState } from "react";
import "summernote/dist/summernote-lite.css";
import "bootstrap/dist/css/bootstrap.css";

const SummernoteEditor = ({
  value = "",
  onChange,
  placeholder = "Enter text...",
  height = 350,
  className = "",
  disabled = false,
  contentKey = "", // Add contentKey prop for unique identification
}) => {
  const editorRef = useRef(null);
  const summernoteRef = useRef(null);
  const [isInitialized, setIsInitialized] = useState(false);
  const [pendingValue, setPendingValue] = useState(value);
  const [isUserEditing, setIsUserEditing] = useState(false);
  const [hasSetInitialValue, setHasSetInitialValue] = useState(false);

  // Load & Initialize Summernote
  useEffect(() => {
    const loadAndInit = async () => {
      try {
        const $ = (await import("jquery")).default;
        window.$ = $;
        window.jQuery = $;

        await import("summernote/dist/summernote-lite.js");

        if (editorRef.current && !summernoteRef.current) {
          $(editorRef.current).summernote({
            height,
            placeholder,
            toolbar: [
              ["style", ["style"]],
              ["font", ["bold", "underline", "clear"]],
              ["fontname", ["fontname"]],
              ["para", ["ul", "ol", "paragraph"]],
              ["table", ["table"]],
              ["insert", ["link", "picture", "video"]],
              ["view", ["fullscreen", "codeview"]],
            ],
            callbacks: {
              onChange: function (contents) {
                setIsUserEditing(true);
                onChange?.(contents);

                // Reset user editing flag after a delay
                setTimeout(() => setIsUserEditing(false), 1000);
              },
              onFocus: function() {
                setIsUserEditing(true);
              },
              onBlur: function() {
                setTimeout(() => setIsUserEditing(false), 100);
              },
            },
          });

          summernoteRef.current = $(editorRef.current);

          setIsInitialized(true);

          // Set initial value after a small delay to ensure editor is fully ready
          setTimeout(() => {
            const initialValue = pendingValue || value || "";
            if (initialValue && !hasSetInitialValue) {
              $(editorRef.current).summernote("code", initialValue);
              setHasSetInitialValue(true);
            }

            setPendingValue(""); // Clear pending value after initialization
          }, 100);

          if (disabled) {
            $(editorRef.current).summernote("disable");
          }
        }
      } catch (err) {
        console.error("Failed to load Summernote:", err);
      }
    };

    loadAndInit();

    return () => {
      if (summernoteRef.current) {
        try {
          summernoteRef.current.summernote("destroy");
          summernoteRef.current = null;
        } catch (error) {
          console.error("Error destroying Summernote:", error);
        }
      }
      setIsInitialized(false);
      setIsUserEditing(false);
      setHasSetInitialValue(false);
    };
  }, [contentKey]); // Re-initialize when contentKey changes

  // Update editor when `value` changes (Edit mode async data) - but only if user is not actively editing
  useEffect(() => {
    if (value !== undefined && value !== null && !isUserEditing) {
      if (isInitialized && summernoteRef.current && hasSetInitialValue) {
        const current = summernoteRef.current.summernote("code");
        // Only update if the values are significantly different (not just minor formatting differences)
        if (current !== value && !current.includes(value) && !value.includes(current)) {
          summernoteRef.current.summernote("code", value);
        }
      } else if (!isInitialized) {
        // Store value to set when editor is initialized
        setPendingValue(value);
      }
    }
  }, [value, isInitialized, isUserEditing, hasSetInitialValue]);

  // Immediate content setting when both editor and value are ready - but only if user is not editing
  useEffect(() => {
    if (isInitialized && summernoteRef.current && value && value.trim() !== "" && !isUserEditing && !hasSetInitialValue) {
      const current = summernoteRef.current.summernote("code");
      if (current !== value && (current.trim() === "" || current === "<p><br></p>")) {
        summernoteRef.current.summernote("code", value);
        setHasSetInitialValue(true);
      }
    }
  }, [isInitialized, value, isUserEditing, hasSetInitialValue]);

  // Fallback mechanism to ensure content is set after a delay - only if user is not editing and initial value hasn't been set
  useEffect(() => {
    if (isInitialized && summernoteRef.current && !isUserEditing && !hasSetInitialValue) {
      const timeoutId = setTimeout(() => {
        const current = summernoteRef.current.summernote("code");
        const targetValue = value || pendingValue;

        if (targetValue && (current !== targetValue && (current.trim() === "" || current === "<p><br></p>"))) {
          summernoteRef.current.summernote("code", targetValue);
          setHasSetInitialValue(true);
        }
      }, 1000); // Wait 1000ms before fallback

      return () => clearTimeout(timeoutId);
    }
  }, [isInitialized, value, pendingValue, isUserEditing, hasSetInitialValue]);

  return (
    <div className={`summernote-wrapper ${className}`}>
      <div ref={editorRef}></div>
    </div>
  );
};

export default SummernoteEditor;


// import React, { useEffect, useRef, useState } from "react";
// import "summernote/dist/summernote-lite.css";
// import "bootstrap/dist/css/bootstrap.css";

// const SummernoteEditor = ({
//   value = "",
//   onChange,
//   placeholder = "Enter text...",
//   height = 350,
//   className = "",
//   disabled = false
// }) => {
//   const editorRef = useRef(null);
//   const summernoteRef = useRef(null);
//   const [isInitialized, setIsInitialized] = useState(false);

//   useEffect(() => {
//     const init = async () => {
//       const $ = (await import("jquery")).default;
//       window.$ = $;
//       window.jQuery = $;
//       await import("summernote/dist/summernote-lite.js");

//       if (editorRef.current && !isInitialized) {
//         $(editorRef.current).summernote({
//           height,
//           placeholder,
//           toolbar: [
//             ["style", ["style"]],
//             ["font", ["bold", "underline", "clear"]],
//             ["fontname", ["fontname"]],
//             ["para", ["ul", "ol", "paragraph"]],
//             ["table", ["table"]],
//             ["insert", ["link", "picture", "video"]],
//             ["view", ["fullscreen", "codeview"]],
//           ],
//           callbacks: {
//             onChange: function (contents) {
//               onChange?.(contents);
//             },
//           },
//         });

//         summernoteRef.current = $(editorRef.current);
//         summernoteRef.current.summernote("code", value); // ✅ SET VALUE
//         if (disabled) summernoteRef.current.summernote("disable");
//         setIsInitialized(true);
//       }
//     };

//     if (value && !isInitialized) {
//       init();
//     }
//   }, [value, isInitialized]);

//   return (
//     <div className={`summernote-wrapper ${className}`}>
//       <div ref={editorRef}></div>
//     </div>
//   );
// };

// export default SummernoteEditor;
