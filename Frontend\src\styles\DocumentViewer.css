/* Document Viewer Styles */
.document-viewer {
  position: relative;
  width: 100%;
  border-radius: var(--border-radius);
  overflow: hidden;
  background: var(--white);
  border: 1px solid var(--light-gray);
}

.document-viewer__preview-card {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: var(--heading4);
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.document-viewer__header {
  display: flex;
  align-items: flex-start;
  gap: var(--heading6);
  margin-bottom: var(--heading4);
}

.document-viewer__icon-container {
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 80px;
  height: 80px;
  border-radius: var(--border-radius);
  background: var(--white);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.document-viewer__icon {
  font-size: 2.5rem;
}

.document-viewer__icon--word {
  color: #2b579a;
}

.document-viewer__icon--excel {
  color: #217346;
}

.document-viewer__icon--powerpoint {
  color: #d24726;
}

.document-viewer__icon--pdf {
  color: #dc3545;
}

.document-viewer__icon--text {
  color: #6c757d;
}

.document-viewer__icon--default {
  color: var(--dark-gray);
}

.document-viewer__info {
  flex: 1;
  min-width: 0;
}

.document-viewer__title {
  margin: 0 0 var(--smallfont) 0;
  font-size: var(--heading6);
  font-weight: 600;
  color: var(--text-color);
  line-height: 1.3;
  word-break: break-word;
}

.document-viewer__type {
  margin: 0 0 var(--smallfont) 0;
  font-size: var(--basefont);
  font-weight: 500;
  color: var(--primary-color);
}

.document-viewer__filename {
  margin: 0 0 var(--smallfont) 0;
  font-size: var(--smallfont);
  color: var(--dark-gray);
  word-break: break-all;
  font-family: 'Courier New', monospace;
}

.document-viewer__extension {
  margin: 0;
  font-size: var(--smallfont);
  color: var(--medium-gray);
  font-weight: 500;
  text-transform: uppercase;
}

.document-viewer__content {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
  padding: var(--heading4);
  background: var(--white);
  border-radius: var(--border-radius);
  border: 2px dashed var(--light-gray);
}

.document-viewer__preview-message {
  margin-bottom: var(--heading4);
}

.document-viewer__preview-message p {
  margin: 0 0 var(--smallfont) 0;
  color: var(--dark-gray);
  font-size: var(--basefont);
  line-height: 1.5;
}

.document-viewer__preview-message p:last-child {
  margin-bottom: 0;
}

.document-viewer__download-button {
  display: inline-flex;
  align-items: center;
  gap: var(--smallfont);
  padding: var(--basefont) var(--heading6);
  background: var(--primary-color);
  color: var(--white);
  border: none;
  border-radius: var(--border-radius);
  font-size: var(--basefont);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
}

.document-viewer__download-button:hover {
  background: var(--primary-dark);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.document-viewer__download-button:active {
  transform: translateY(0);
}

/* Download overlay for PDF viewer */
.document-viewer__download-overlay {
  position: absolute;
  top: var(--basefont);
  right: var(--basefont);
  z-index: 10;
}

.document-viewer__download-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: var(--primary-color);
  color: var(--white);
  border: none;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.document-viewer__download-btn:hover {
  background: var(--primary-dark);
  transform: scale(1.1);
}

.document-viewer__download-btn:active {
  transform: scale(0.95);
}

/* Responsive design */
@media (max-width: 768px) {
  .document-viewer__preview-card {
    padding: var(--heading6);
  }
  
  .document-viewer__header {
    flex-direction: column;
    align-items: center;
    text-align: center;
    gap: var(--basefont);
  }
  
  .document-viewer__icon-container {
    width: 60px;
    height: 60px;
  }
  
  .document-viewer__icon {
    font-size: 2rem;
  }
  
  .document-viewer__title {
    font-size: var(--basefont);
  }
  
  .document-viewer__content {
    padding: var(--heading6);
  }
  
  .document-viewer__download-button {
    padding: var(--smallfont) var(--basefont);
    font-size: var(--smallfont);
  }
}

@media (max-width: 480px) {
  .document-viewer__preview-card {
    padding: var(--basefont);
  }
  
  .document-viewer__icon-container {
    width: 50px;
    height: 50px;
  }
  
  .document-viewer__icon {
    font-size: 1.5rem;
  }
  
  .document-viewer__download-overlay {
    top: var(--smallfont);
    right: var(--smallfont);
  }
  
  .document-viewer__download-btn {
    width: 35px;
    height: 35px;
  }
}
