/* Word Document Viewer Styles */
.word-document-viewer {
  display: flex;
  flex-direction: column;
  background-color: var(--white);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  overflow: hidden;
  position: relative;
}

/* Header */
.word-document-viewer__header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--smallfont) var(--basefont);
  background-color: var(--light-gray);
  border-bottom: 1px solid var(--border-color);
  min-height: 60px;
}

.word-document-viewer__info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.word-document-viewer__title {
  font-size: var(--smallfont);
  font-weight: 600;
  color: var(--secondary-color);
  line-height: 1.2;
}

.word-document-viewer__type {
  font-size: var(--tinyfont);
  color: #2b579a; /* Word blue color */
  line-height: 1.2;
  font-weight: 500;
}

.word-document-viewer__stats {
  font-size: var(--tinyfont);
  color: var(--text-muted);
  line-height: 1.2;
}

.word-document-viewer__download-btn {
  background: var(--primary-color);
  color: var(--white);
  border: none;
  border-radius: var(--border-radius);
  padding: var(--tinyfont) var(--smallfont);
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: var(--tinyfont);
  font-size: var(--smallfont);
  transition: all 0.3s ease;
  min-width: 40px;
  height: 36px;
  justify-content: center;
}

.word-document-viewer__download-btn:hover {
  background: var(--primary-dark);
  transform: translateY(-1px);
}

/* Content area */
.word-document-viewer__content {
  flex: 1;
  overflow-y: auto;
  background-color: var(--white);
}

.word-document-viewer__html-content {
  padding: var(--heading5);
  line-height: 1.6;
  font-family: 'Times New Roman', serif;
  color: var(--text-color);
  max-width: 100%;
  word-wrap: break-word;
}

/* Style the converted HTML content */
.word-document-viewer__html-content h1,
.word-document-viewer__html-content h2,
.word-document-viewer__html-content h3,
.word-document-viewer__html-content h4,
.word-document-viewer__html-content h5,
.word-document-viewer__html-content h6 {
  color: var(--secondary-color);
  margin: var(--basefont) 0 var(--smallfont) 0;
  font-weight: 600;
}

.word-document-viewer__html-content p {
  margin: 0 0 var(--smallfont) 0;
  text-align: justify;
}

.word-document-viewer__html-content ul,
.word-document-viewer__html-content ol {
  margin: var(--smallfont) 0;
  padding-left: var(--heading5);
}

.word-document-viewer__html-content li {
  margin-bottom: var(--tinyfont);
}

.word-document-viewer__html-content table {
  width: 100%;
  border-collapse: collapse;
  margin: var(--basefont) 0;
  font-size: var(--smallfont);
}

.word-document-viewer__html-content table th,
.word-document-viewer__html-content table td {
  border: 1px solid var(--border-color);
  padding: var(--tinyfont) var(--smallfont);
  text-align: left;
}

.word-document-viewer__html-content table th {
  background-color: var(--light-gray);
  font-weight: 600;
}

.word-document-viewer__html-content img {
  max-width: 100%;
  height: auto;
  margin: var(--smallfont) 0;
  border-radius: var(--border-radius);
}

.word-document-viewer__html-content blockquote {
  border-left: 4px solid var(--primary-color);
  margin: var(--basefont) 0;
  padding-left: var(--basefont);
  font-style: italic;
  color: var(--dark-gray);
}

/* No content state */
.word-document-viewer__no-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  min-height: 200px;
  padding: var(--heading5);
  text-align: center;
  color: var(--text-muted);
}

.word-document-viewer__no-content svg {
  font-size: var(--heading3);
  color: #2b579a;
  margin-bottom: var(--basefont);
}

.word-document-viewer__no-content p {
  margin: 0 0 var(--tinyfont) 0;
  font-size: var(--smallfont);
}

/* Loading state */
.word-document-viewer__loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  min-height: 300px;
  color: var(--text-muted);
  background-color: var(--light-gray);
}

.word-document-viewer__loading .spinning {
  font-size: var(--heading5);
  margin-bottom: var(--basefont);
  animation: spin 1s linear infinite;
}

.word-document-viewer__loading p {
  margin: 0;
  font-size: var(--smallfont);
  text-align: center;
}

.word-document-viewer__loading-info {
  color: var(--primary-color) !important;
  font-size: var(--tinyfont) !important;
  margin-top: var(--tinyfont) !important;
}

/* Error state */
.word-document-viewer__error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  min-height: 300px;
  padding: var(--heading5);
  text-align: center;
  color: var(--text-muted);
  background-color: var(--light-gray);
}

.word-document-viewer__error svg {
  font-size: var(--heading3);
  color: var(--warning-color);
  margin-bottom: var(--basefont);
}

.word-document-viewer__error h3 {
  margin: 0 0 var(--smallfont) 0;
  font-size: var(--heading6);
  color: var(--secondary-color);
}

.word-document-viewer__error p {
  margin: 0 0 var(--tinyfont) 0;
  font-size: var(--smallfont);
  line-height: 1.4;
}

.word-document-viewer__error-details {
  color: var(--error-color) !important;
  font-size: var(--tinyfont) !important;
  font-family: monospace !important;
}

.word-document-viewer__download-button {
  background: var(--primary-color);
  color: var(--white);
  border: none;
  border-radius: var(--border-radius);
  padding: var(--smallfont) var(--basefont);
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: var(--tinyfont);
  font-size: var(--smallfont);
  margin-top: var(--basefont);
  transition: all 0.3s ease;
}

.word-document-viewer__download-button:hover {
  background: var(--primary-dark);
  transform: translateY(-1px);
}

/* Footer */
.word-document-viewer__footer {
  border-top: 1px solid var(--border-color);
  padding: var(--tinyfont) var(--basefont);
  background-color: var(--light-gray);
}

.word-document-viewer__metadata {
  display: flex;
  gap: var(--basefont);
  flex-wrap: wrap;
}

.word-document-viewer__meta-item {
  font-size: var(--tinyfont);
  color: var(--text-muted);
  padding: 2px var(--tinyfont);
  background-color: var(--white);
  border-radius: 3px;
  border: 1px solid var(--border-color);
}

/* Responsive design */
@media (max-width: 768px) {
  .word-document-viewer__header {
    padding: var(--tinyfont) var(--smallfont);
    min-height: 50px;
  }
  
  .word-document-viewer__title {
    font-size: var(--tinyfont);
  }
  
  .word-document-viewer__type,
  .word-document-viewer__stats {
    font-size: 10px;
  }
  
  .word-document-viewer__download-btn {
    min-width: 35px;
    height: 32px;
    padding: var(--tinyfont);
  }
  
  .word-document-viewer__html-content {
    padding: var(--basefont);
    font-size: var(--smallfont);
  }
  
  .word-document-viewer__loading,
  .word-document-viewer__error {
    min-height: 250px;
    padding: var(--basefont);
  }
}

/* Animation for spinning icon */
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
