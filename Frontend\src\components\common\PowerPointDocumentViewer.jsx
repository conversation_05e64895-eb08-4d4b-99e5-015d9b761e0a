import React, { useState, useEffect } from 'react';
import { FaDownload, FaExclamationTriangle, FaSync, FaFilePowerpoint, FaCalendar, FaFileAlt } from 'react-icons/fa';
import { API_BASE_URL } from '../../utils/constants';
import '../../styles/PowerPointDocumentViewer.css';

const PowerPointDocumentViewer = ({ 
  fileUrl, 
  fileName = '',
  title = 'PowerPoint Presentation',
  className = '',
  height = '400px',
  showDownload = false,
  onDownload = null
}) => {
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);
  const [documentData, setDocumentData] = useState(null);
  const [errorMessage, setErrorMessage] = useState('');

  // Extract filename from fileUrl for API call
  const getFileName = () => {
    if (fileName) return fileName;
    if (fileUrl) return fileUrl.split('/').pop();
    return 'document.pptx';
  };

  // Format file size
  const formatFileSize = (bytes) => {
    if (!bytes) return 'Unknown size';
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
  };

  // Format date
  const formatDate = (dateString) => {
    if (!dateString) return 'Unknown';
    try {
      return new Date(dateString).toLocaleDateString();
    } catch {
      return 'Unknown';
    }
  };

  // Load document preview
  useEffect(() => {
    const loadDocumentPreview = async () => {
      if (!fileUrl) {
        setHasError(true);
        setErrorMessage('No file URL provided');
        setIsLoading(false);
        return;
      }

      try {
        setIsLoading(true);
        setHasError(false);

        const actualFileName = getFileName();
        console.log(`[PowerPointViewer] Loading preview for: ${actualFileName}`);

        // Check if fileUrl is a JSON preview file
        if (fileUrl.endsWith('.json')) {
          console.log(`[PowerPointViewer] Loading JSON preview from: ${fileUrl}`);

          // Fetch the JSON preview directly
          const response = await fetch(fileUrl);
          if (!response.ok) {
            throw new Error(`Failed to load preview: ${response.status}`);
          }

          const previewData = await response.json();
          if (previewData.success) {
            setDocumentData(previewData);
          } else {
            throw new Error(previewData.error || 'Invalid preview data');
          }
        } else {
          // Call our custom document preview API for live conversion
          const response = await fetch(`${API_BASE_URL}/document-preview/convert`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${localStorage.getItem('token')}`
            },
            body: JSON.stringify({
              fileUrl: fileUrl,
              fileName: actualFileName
            })
          });

          if (!response.ok) {
            throw new Error(`Preview generation failed: ${response.status}`);
          }

          const result = await response.json();

          if (!result.success) {
            throw new Error(result.message || 'Failed to generate preview');
          }

          setDocumentData(result.data);
        }

        console.log(`[PowerPointViewer] Preview loaded successfully`);
        setIsLoading(false);

      } catch (error) {
        console.error('[PowerPointViewer] Preview loading failed:', error);
        setHasError(true);
        setErrorMessage(error.message);
        setIsLoading(false);
      }
    };

    loadDocumentPreview();
  }, [fileUrl, fileName]);

  // Handle download
  const handleDownload = () => {
    if (onDownload) {
      onDownload();
    } else if (fileUrl) {
      window.open(fileUrl, '_blank');
    }
  };

  // Render loading state
  if (isLoading) {
    return (
      <div className={`powerpoint-document-viewer ${className}`} style={{ height }}>
        <div className="powerpoint-document-viewer__header">
          <div className="powerpoint-document-viewer__info">
            <span className="powerpoint-document-viewer__title">{title}</span>
            <span className="powerpoint-document-viewer__type">Microsoft PowerPoint Presentation</span>
          </div>
          {/* {showDownload && (
            <button 
              className="powerpoint-document-viewer__download-btn"
              onClick={handleDownload}
              title="Download Document"
            >
              <FaDownload />
            </button>
          )} */}
        </div>
        
        <div className="powerpoint-document-viewer__loading">
          <FaSync className="spinning" />
          <p>Analyzing PowerPoint presentation...</p>
          <p className="powerpoint-document-viewer__loading-info">
            Extracting document information...
          </p>
        </div>
      </div>
    );
  }

  // Render error state
  if (hasError) {
    return (
      <div className={`powerpoint-document-viewer ${className}`} style={{ height }}>
        <div className="powerpoint-document-viewer__header">
          <div className="powerpoint-document-viewer__info">
            <span className="powerpoint-document-viewer__title">{title}</span>
            <span className="powerpoint-document-viewer__type">Microsoft PowerPoint Presentation</span>
          </div>
          {/* {showDownload && (
            <button 
              className="powerpoint-document-viewer__download-btn"
              onClick={handleDownload}
              title="Download Document"
            >
              <FaDownload />
            </button>
          )} */}
        </div>
        
        <div className="powerpoint-document-viewer__error">
          <FaExclamationTriangle />
          <h3>Preview Not Available</h3>
          <p>Unable to generate preview for this PowerPoint presentation.</p>
          <p className="powerpoint-document-viewer__error-details">{errorMessage}</p>
          
          {showDownload && (
            <button 
              className="powerpoint-document-viewer__download-button"
              onClick={handleDownload}
            >
              <FaDownload />
              Download PowerPoint Presentation
            </button>
          )}
        </div>
      </div>
    );
  }

  // Render document content
  return (
    <div className={`powerpoint-document-viewer ${className}`} style={{ height }}>

      <div className="powerpoint-document-viewer__content">
        {/* Show slide preview if available */}
        {documentData?.preview?.slides && documentData.preview.slides.length > 0 ? (
          <div className="powerpoint-document-viewer__slides-preview">
            <div className="powerpoint-document-viewer__preview-header">
              <FaFilePowerpoint className="powerpoint-document-viewer__main-icon" />
              <h3>Presentation Preview</h3>

            </div>

            <div className="powerpoint-slides-container">
              {documentData.preview.slides.map((slide, index) => (
                <div key={index} className="powerpoint-slide-preview">
                  <div className="powerpoint-slide-header">
                    <span className="powerpoint-slide-number">Slide {slide.slideNumber}</span>
                    {slide.title && <h4 className="powerpoint-slide-title">{slide.title}</h4>}
                  </div>

                  <div className="powerpoint-slide-content">
                    <p>{slide.content}</p>
                    {slide.notes && (
                      <div className="powerpoint-slide-notes">
                        <small><strong>Notes:</strong> {slide.notes}</small>
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>


          </div>
        ) : (
          <div className="powerpoint-document-viewer__preview-card">
            <div className="powerpoint-document-viewer__icon-section">
              <FaFilePowerpoint className="powerpoint-document-viewer__main-icon" />
              <h3>PowerPoint Presentation</h3>
              <p>Full content preview requires download</p>
            </div>

            {documentData?.metadata && (
              <div className="powerpoint-document-viewer__metadata-grid">
                <div className="powerpoint-metadata-item">
                  <FaFileAlt className="powerpoint-metadata-icon" />
                  <div className="powerpoint-metadata-content">
                    <span className="powerpoint-metadata-label">File Name</span>
                    <span className="powerpoint-metadata-value">{documentData.metadata.fileName}</span>
                  </div>
                </div>

                <div className="powerpoint-metadata-item">
                  <FaCalendar className="powerpoint-metadata-icon" />
                  <div className="powerpoint-metadata-content">
                    <span className="powerpoint-metadata-label">Last Modified</span>
                    <span className="powerpoint-metadata-value">{formatDate(documentData.metadata.lastModified)}</span>
                  </div>
                </div>

                <div className="powerpoint-metadata-item">
                  <FaFileAlt className="powerpoint-metadata-icon" />
                  <div className="powerpoint-metadata-content">
                    <span className="powerpoint-metadata-label">File Size</span>
                    <span className="powerpoint-metadata-value">{formatFileSize(documentData.metadata.fileSize)}</span>
                  </div>
                </div>

                <div className="powerpoint-metadata-item">
                  <FaFilePowerpoint className="powerpoint-metadata-icon" />
                  <div className="powerpoint-metadata-content">
                    <span className="powerpoint-metadata-label">File Type</span>
                    <span className="powerpoint-metadata-value">{documentData.metadata.fileType}</span>
                  </div>
                </div>
              </div>
            )}


          </div>
        )}


      </div>
    </div>
  );
};

export default PowerPointDocumentViewer;
