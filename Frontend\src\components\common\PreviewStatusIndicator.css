.preview-status {
  padding: 12px 16px;
  border-radius: 8px;
  margin: 16px 0;
  border: 1px solid;
  transition: all 0.3s ease;
}

.preview-status__content {
  display: flex;
  align-items: center;
  gap: 8px;
}

.preview-status__icon {
  font-size: 18px;
  flex-shrink: 0;
}

.preview-status__message {
  font-weight: 500;
  flex: 1;
}

.preview-status__spinner {
  margin-left: auto;
}

.preview-status__error-details {
  margin-top: 8px;
  padding-top: 8px;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
}

.preview-status__error-details small {
  color: #666;
  font-size: 12px;
}

.preview-status__success-note {
  margin-top: 8px;
  padding-top: 8px;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
}

.preview-status__success-note small {
  color: #28a745;
  font-size: 12px;
  font-weight: 500;
}

/* Status-specific styles */
.preview-status--pending {
  background-color: #fff3cd;
  border-color: #ffeaa7;
  color: #856404;
}

.preview-status--processing {
  background-color: #d1ecf1;
  border-color: #bee5eb;
  color: #0c5460;
}

.preview-status--completed {
  background-color: #d4edda;
  border-color: #c3e6cb;
  color: #155724;
}

.preview-status--failed {
  background-color: #f8d7da;
  border-color: #f5c6cb;
  color: #721c24;
}

.preview-status--not_supported {
  background-color: #e2e3e5;
  border-color: #d6d8db;
  color: #383d41;
}

/* Icon colors */
.preview-status__icon--pending {
  color: #ffc107;
}

.preview-status__icon--processing {
  color: #17a2b8;
  animation: spin 1s linear infinite;
}

.preview-status__icon--completed {
  color: #28a745;
}

.preview-status__icon--failed {
  color: #dc3545;
}

.preview-status__icon--not-supported {
  color: #6c757d;
}

/* Spinner animation */
.spinner {
  width: 16px;
  height: 16px;
  border: 2px solid #f3f3f3;
  border-top: 2px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive design */
@media (max-width: 768px) {
  .preview-status {
    padding: 10px 12px;
    margin: 12px 0;
  }
  
  .preview-status__content {
    gap: 6px;
  }
  
  .preview-status__icon {
    font-size: 16px;
  }
  
  .preview-status__message {
    font-size: 14px;
  }
}
