.stripe-payment-form {
  max-width: 100%;
  margin: 0;
  padding: 0;
  background: transparent;
  border-radius: 0;
  box-shadow: none;
}

.payment-header {
  text-align: left;
  margin-bottom: var(--heading5);
  padding-bottom: var(--heading6);
  border-bottom: 1px solid var(--light-gray);
}

.payment-header h3 {
  font-size: var(--heading5);
  font-weight: 600;
  color: var(--secondary-color);
  margin-bottom: var(--smallfont);
}

.payment-header p {
  color: var(--dark-gray);
  font-size: var(--smallfont);
  margin: 0;
}

.payment-form {
  display: flex;
  flex-direction: column;
  gap: var(--heading5);
}

.billing-details {
  display: flex;
  flex-direction: column;
  gap: var(--basefont);
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: var(--smallfont);
}

.form-group label {
  font-weight: 600;
  color: var(--secondary-color);
  font-size: var(--smallfont);
  margin-bottom: 6px;
}

.form-input {
  padding: var(--basefont) var(--heading6);
  border: 2px solid var(--light-gray);
  border-radius: var(--border-radius-medium);
  font-size: var(--basefont);
  color: var(--text-color);
  background-color: var(--white);
  transition: all 0.3s ease;
  font-family: inherit;
  min-height: 50px;
  position: relative;
}

.form-input:focus {
  outline: none;
  border-color: var(--btn-color);
  box-shadow: 0 0 0 3px rgba(238, 52, 37, 0.1);
  transform: translateY(-1px);
}

.form-input:hover {
  border-color: var(--btn-color);
  box-shadow: 0 2px 8px rgba(238, 52, 37, 0.1);
}

.card-element-container {
  padding: var(--basefont) var(--heading6);
  border: 2px solid var(--light-gray);
  border-radius: var(--border-radius-medium);
  background: var(--white);
  transition: all 0.3s ease;
  min-height: 50px;
  display: flex;
  align-items: center;
  position: relative;
  overflow: hidden;
}

.card-element-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background: transparent;
  transition: background 0.3s ease;
}

.card-element-container:focus-within {
  border-color: var(--btn-color);
  box-shadow: 0 0 0 3px rgba(238, 52, 37, 0.1);
  transform: translateY(-1px);
}

.card-element-container:focus-within::before {
  background: var(--btn-color);
}

.card-element-container:hover {
  border-color: var(--btn-color);
  box-shadow: 0 2px 8px rgba(238, 52, 37, 0.1);
}

.card-element-loading {
  display: flex;
  align-items: center;
  gap: var(--smallfont);
  color: var(--dark-gray);
  font-size: var(--smallfont);
  padding: var(--smallfont) 0;
}

.card-element-loading .spinner {
  width: 16px;
  height: 16px;
  border: 2px solid var(--light-gray);
  border-top: 2px solid var(--btn-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.card-element-placeholder {
  display: flex;
  align-items: center;
  color: var(--dark-gray);
  font-size: var(--smallfont);
  padding: var(--smallfont) 0;
  opacity: 0.7;
}

.stripe-card-element {
  width: 100%;
 
  min-height: 20px;
}

.stripe-card-element-wrapper {
  width: 100%;
  min-height: 40px;
  display: flex;
  align-items: center;
}

.card-error {
  color: var(--error-color);
  font-size: var(--smallfont);
  margin-top: 6px;
  padding: var(--smallfont);
  background: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: var(--border-radius);
  display: flex;
  align-items: center;
  gap: 8px;
}

.card-error::before {
  content: "⚠️";
  font-size: var(--basefont);
}

.payment-error {
  background: #fef2f2;
  border: 1px solid #fecaca;
  color: var(--error-color);
  padding: var(--basefont);
  border-radius: var(--border-radius-medium);
  text-align: center;
}

.order-summary-payment {
  background: var(--bg-gray);
  padding: var(--heading6);
  border-radius: var(--border-radius-medium);
  border: 1px solid var(--light-gray);
  margin-top: var(--basefont);
}

.summary-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--smallfont) 0;
  font-size: var(--smallfont);
  color: var(--text-color);
}

.summary-row.total {
  border-top: 1px solid var(--light-gray);
  margin-top: var(--smallfont);
  padding-top: var(--basefont);
  font-weight: 600;
  font-size: var(--basefont);
  color: var(--secondary-color);
}

.payment-actions {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--basefont);
  margin-top: var(--heading6);
  padding-top: var(--heading6);
  border-top: 1px solid var(--light-gray);
}

.cancel-btn {
  flex: 1;
  padding: var(--smallfont) var(--heading5);
  border: 1px solid var(--light-gray);
  background: var(--white);
  color: var(--dark-gray);
  border-radius: var(--border-radius-medium);
  font-weight: 500;
  font-size: var(--basefont);
  cursor: pointer;
  transition: all 0.3s ease;
  font-family: inherit;
}

.cancel-btn:hover:not(:disabled) {
  background: var(--bg-gray);
  border-color: var(--dark-gray);
  transform: translateY(-1px);
  box-shadow: var(--box-shadow-light);
}

.pay-btn {
  flex: 2;

  

 

  font-weight: 600;

  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--smallfont);
  
}

.pay-btn:hover:not(:disabled) {
  
  transform: translateY(-2px);

}

.pay-btn:disabled {
  background: var(--dark-gray);
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.pay-btn.processing {
  position: relative;
  color: transparent;
}

.pay-btn.processing::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 20px;
  height: 20px;
  border: 2px solid transparent;
  border-top: 2px solid var(--white);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: translate(-50%, -50%) rotate(0deg); }
  100% { transform: translate(-50%, -50%) rotate(360deg); }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.payment-method-selection,
.billing-details,
.form-group {
  animation: fadeInUp 0.6s ease-out;
}

.spinner {
  width: 16px;
  height: 16px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.security-notice {
  text-align: center;
  margin-top: var(--heading5);
  padding: var(--basefont);
  background: linear-gradient(135deg, var(--bg-blue) 0%, var(--primary-light-color) 100%);
  border-radius: var(--border-radius-medium);
  border: 1px solid var(--light-gray);
}

.security-notice p {
  color: var(--dark-gray);
  font-size: var(--smallfont);
  margin: 0;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--smallfont);
}

/* Responsive Design */
@media (max-width: 768px) {
  .payment-header h3 {
    font-size: var(--heading6);
  }

  .payment-actions {
    grid-template-columns: 1fr;
    gap: var(--smallfont);
  }

  .cancel-btn,
  .pay-btn {
    flex: none;
    padding: var(--basefont) var(--heading5);
  }

  .form-input {
    font-size: var(--basefont);
  }

  .payment-method-selection {
    padding: var(--basefont);
  }

 


  .card-info {
    gap: var(--smallfont);
  }

  .card-icon {
    min-width: 36px;
    height: 36px;
    font-size: var(--basefont);
  }
}

/* Loading state overlay */
.payment-form.processing {
  position: relative;
  pointer-events: none;
}

.payment-form.processing::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 8px;
  z-index: 10;
}

/* Payment Method Selection Styles */
.payment-method-selection {
  margin-bottom: var(--heading5);
  padding: var(--heading6);
  border: 2px solid var(--light-gray);
  border-radius: var(--border-radius-large);

  box-shadow: var(--box-shadow-light);
  position: relative;
  overflow: hidden;
}



.payment-method-selection h4 {
  margin: 0 0 var(--heading6) 0;
  color: var(--secondary-color);
  font-size: var(--heading6);
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: var(--smallfont);
  padding-bottom: var(--smallfont);
  border-bottom: 1px solid var(--light-gray);
}


.payment-option {
  margin-bottom: var(--basefont);
  padding: var(--basefont);
  border-radius: var(--border-radius-medium);
  transition: all 0.3s ease;
  border: 1px solid transparent;
  background: rgba(255, 255, 255, 0.7);
  display: flex;
  align-items: center;
  gap: 10px;
}

.payment-option:last-child {
  margin-bottom: 0;
}

.payment-option:hover {
  background: rgba(var(--primary-rgb), 0.05);
  border-color: var(--btn-color);
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(238, 52, 37, 0.1);
}

.payment-option input[type="radio"] {
  margin-right: var(--basefont);
  accent-color: var(--btn-color);
  width: 20px;
  height: 20px;
  cursor: pointer;
}

.payment-option label {
  display: flex;
  align-items: center;
  cursor: pointer;
  font-weight: 500;
  color: var(--secondary-color);
  font-size: var(--basefont);
  transition: all 0.3s ease;
  width: 100%;
}

.payment-option input[type="radio"]:checked + label {
  color: var(--btn-color);
  font-weight: 600;
}

.payment-option:has(input[type="radio"]:checked) {
  background: transparent;
  border-color: var(--btn-color);
  box-shadow: 0 0 0 2px rgba(238, 52, 37, 0.1);
}

.plus-icon {
  margin-right: var(--smallfont);
  color: var(--btn-color);
  font-size: var(--basefont);
}

/* Saved Cards List */
.saved-cards-section {
margin-bottom:var(--basefont) ;
  margin-top: var(--basefont);
}

.saved-cards-list {
  margin-top: var(--basefont);
  margin-bottom: var(--basefont);
  display: flex;
  flex-direction: column;
  gap: var(--basefont);
  padding: var(--basefont);
  background: rgba(255, 255, 255, 0.5);
  border-radius: var(--border-radius-medium);
  border: 1px solid var(--light-gray);
}

.saved-card-item {
  padding: var(--extrasmallfont);
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius-medium);
  background: var(--white);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
  cursor: pointer;
}



.saved-card-item:hover {
  border-color: var(--btn-color);
  box-shadow: 0 4px 12px rgba(238, 52, 37, 0.15);
  transform: translateY(-2px);
}



.saved-card-item:has(input[type="radio"]:checked) {
  border-color: var(--btn-color);
  background: rgba(238, 52, 37, 0.02);
  box-shadow: 0 0 0 2px rgba(238, 52, 37, 0.1);
}

.saved-card-item:has(input[type="radio"]:checked)::before {
  background: var(--btn-color);
}

.saved-card-item input[type="radio"]:checked + .card-label {
  color: var(--btn-color);
}

.saved-card-item input[type="radio"] {
  margin-right: var(--basefont);
  accent-color: var(--btn-color);
  width: 20px;
  height: 20px;
  cursor: pointer;
}

.card-label {
  display: flex;
  align-items: center;
  cursor: pointer;
  width: 100%;
  font-weight: 500;
}

.card-info {
  display: flex;
  align-items: center;
  gap: var(--basefont);
  flex: 1;
}

.card-icon {
  color: var(--dark-gray);
  font-size: var(--heading6);
  padding: var(--smallfont);
  background: var(--bg-gray);
  border-radius: var(--border-radius);
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 40px;
  height: 40px;
}

.card-details {
  display: flex;
justify-content: space-between;
  gap: 10px;
  flex: 1;
}
.cards-details-style{
  display: grid;
  justify-content: flex-start;
  gap: 10px;
  align-items: center;
}
.card-number {
  font-weight: 600;
  color: var(--secondary-color);
  font-size: var(--basefont);
  letter-spacing: 0.5px;
}

.card-type {
  font-size: var(--smallfont);
  color: var(--dark-gray);
  text-transform: uppercase;
  font-weight: 500;
  letter-spacing: 0.5px;
}

.card-expiry {
  font-size: var(--smallfont);
  color: var(--dark-gray);
}

.default-badge {
  display: inline-flex;
  align-items: center;
  padding: 4px var(--smallfont);
  background: linear-gradient(135deg, #28a745, #20c997);
  color: var(--white);
  font-size: var(--extrasmallfont);
  border-radius: var(--border-radius);
  font-weight: 600;
  text-transform: uppercase;
  width: fit-content;
  box-shadow: 0 2px 4px rgba(40, 167, 69, 0.3);
  letter-spacing: 0.5px;
}

.default-badge::before {
  content: "⭐";
  margin-right: 4px;
  font-size: var(--smallfont);
}

/* Responsive Design for Payment Method Selection */
@media (max-width: 768px) {
  .payment-method-selection {
    padding: 16px;
  }

 



  .card-info {
    gap: 8px;
  }

  .card-details {
    gap: 1px;
  }

  .saved-card-item {
    padding: 10px;
  }
}
@media (max-width: 500px) {

  .card-details {
    gap: 1px;
    display: grid;
  }
.payment-method-selection{
  padding: 0px;
  border: none;
  border-radius: 0px;
  box-shadow: none;
}
.saved-cards-list{




  padding: 0px;


  border: none;
}
}