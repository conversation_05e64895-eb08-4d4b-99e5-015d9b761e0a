.BuyerCards {
  display: flex;
  flex-direction: column;
  width: 100%;
}
.buyercardsbordercontainer {
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius-large);
  padding: var(--heading5);
}
.BuyerCards__header {
  display: grid;
  grid-template-columns: 1fr auto;
  align-items: center;
  padding-bottom: var(--heading6);
}

.BuyerCards__subtitle {
  font-size: var(--heading6);
  color: var(--secondary-color);
  font-weight: 500;
  margin: 0;
}

.BuyerCards__add-btn {
  display: flex;
  align-items: center;
  gap: var(--extrasmallfont);
  background-color: transparent;
  color: var(--btn-color);
  border: 1px solid var(--btn-color);
  padding: var(--extrasmallfont) var(--basefont);
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
  font-size: var(--smallfont);
}

.BuyerCards__add-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(238, 52, 37, 0.3);
}

.BuyerCards__cards-list {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--basefont);
}

.BuyerCards__card-item {
  display: grid;
  grid-template-columns: 1fr auto;
  align-items: center;
  padding: var(--basefont);
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius);
  background-color: var(--white);
  margin-bottom: var(--smallfont);
  transition: box-shadow 0.3s ease;
}

.BuyerCards__card-item:hover {
  box-shadow: var(--box-shadow-light);
}

.BuyerCards__card-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.BuyerCards__card-info {
  display: flex;
  align-items: center;
  gap: var(--basefont);
}

.BuyerCards__card-logo {
  display: flex;
  align-items: center;
  flex-shrink: 0;
}

.BuyerCards__card-logo img {
  height: 24px;
  width: auto;
}

.BuyerCards__card-details {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.BuyerCards__card-number {
  font-size: var(--basefont);
  color: var(--text-color);
  letter-spacing: 1px;
  font-weight: 500;
}

.BuyerCards__card-name {
  font-size: var(--smallfont);
  color: var(--dark-gray);
  font-weight: 400;
}

.BuyerCards__card-expiry {
  font-size: var(--extrasmallfont);
  color: var(--dark-gray);
  font-weight: 400;
}

.BuyerCards__card-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.BuyerCards__default-btn {
  background: none;
  border: none;
  color: #ffc107;
  cursor: pointer;
  padding: 8px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  font-size: 16px;
}

.BuyerCards__default-btn:hover:not(:disabled) {
  background-color: rgba(255, 193, 7, 0.1);
  transform: scale(1.1);
}

.BuyerCards__default-btn.active {
  color: #ffc107;
}

.BuyerCards__default-btn:disabled {
  cursor: default;
}

.BuyerCards__delete-btn {
  background: none;
  border: none;
  color: #ff3b30;
  cursor: pointer;
  padding: 8px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.3s ease;
}

.BuyerCards__delete-btn:hover {
  background-color: rgba(255, 59, 48, 0.1);
}

.BuyerCards__empty-state {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  min-height: 200px;
  background-color: var(--bg-gray);
  border-radius: var(--border-radius);
  padding: 32px;
  color: var(--dark-gray);
  font-size: var(--basefont);
  text-align: center;
  gap: 16px;
}

.BuyerCards__empty-icon {
  font-size: 48px;
  color: var(--light-gray);
  margin-bottom: 8px;
}

.BuyerCards__add-first-btn {
  background-color: var(--btn-color);
  color: var(--white);
  border: none;
  padding: 12px 24px;
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
  font-size: var(--basefont);
}

.BuyerCards__add-first-btn:hover {
  background: linear-gradient(
    to bottom,
    var(--primary-color),
    var(--btn-color)
  );
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(238, 52, 37, 0.3);
}

.BuyerCards__loading {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
  font-size: var(--basefont);
  color: var(--dark-gray);
}

.BuyerCards__form-actions {
  display: grid;
  justify-content: space-between;
  margin-top: 24px;
  grid-template-columns: 1fr 1fr;
  gap:1rem
}

.BuyerCards__cancel-btn {
  background-color: transparent;
  color: var(--dark-gray);
  border: 1px solid var(--light-gray);
border-radius: var(--border-radius-medium);
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
  font-size: var(--basefont);
}

.BuyerCards__cancel-btn:hover {
  background-color: rgba(0, 0, 0, 0.05) !important;
  border-color: var(--dark-gray) !important;
}

.BuyerCards__form {
  width: 100%;
}

.BuyerCards__form-row {
  display: flex;
  gap: 16px;
  margin-bottom: 16px;
  align-items: center;
}

.BuyerCards__input-field {
  flex: 1;
  margin-bottom: 16px;
}

.BuyerCards__input-field--full {
  width: 100%;
}

.BuyerCards__input-field--half {
  flex: 1;
}

.BuyerCards__input-field--card-number {
  flex: 1;
  position: relative;
}

.BuyerCards__input-container {
  display: flex;
  align-items: center;
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius);
  background-color: var(--white);
  transition: border-color 0.3s ease, box-shadow 0.3s ease;
  overflow: hidden;
}

.BuyerCards__input-container:focus-within {
  border-color: var(--btn-color);
  box-shadow: 0 0 0 2px rgba(238, 52, 37, 0.1);
}

.BuyerCards__input-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 12px;
  color: var(--dark-gray);
}

.BuyerCards__input {
  width: 100%;
  padding: 12px 16px;
  border: none;
  font-size: var(--basefont);
  color: var(--text-color);
  background-color: transparent;
  outline: none;
}

.BuyerCards__input::placeholder {
  color: var(--dark-gray);
  opacity: 0.7;
}

.BuyerCards__stripe-element {
  width: 100%;
  border: none;
  font-size: var(--basefont);
  color: var(--text-color);
  background-color: transparent;
  outline: none;
  padding: 12px 16px;
  min-Height: '20px';
}

.BuyerCards__submit-btn {

 

  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 600;
  font-size: var(--basefont);
  width: 100%;
}

.BuyerCards__submit-btn:hover {
  background: linear-gradient(
    to bottom,
    var(--primary-color),
    var(--btn-color)
  );
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(238, 52, 37, 0.3);
}

/* Responsive styles */
@media (max-width: 768px) {
  .BuyerCards__header {
    grid-template-columns: 1fr;
    gap: var(--basefont);
  }

  .BuyerCards__add-btn {
    width: 100%;
    justify-content: center;
  }



  .BuyerCards__cancel-btn {
    width: 100%;
    text-align: center;
  }
}

@media (max-width: 350px) {
.BuyerCards__stripe-element{
  padding: 12px 10px;
}
}