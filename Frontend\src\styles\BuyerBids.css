.BuyerBids {
  display: flex;
  flex-direction: column;
  width: 100%;
}

/* Table Styles - Following BuyerDownloads pattern */
.BuyerBids .table {
  width: 100%;
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius);
  overflow: hidden;
}

.BuyerBids .table-header {
  display: grid;
  grid-template-columns: 0.5fr 1fr 3fr 1.5fr 1fr 1fr 0.5fr;
  background-color: var(--bg-gray);
  padding: var(--smallfont) var(--basefont);
  font-weight: 600;
  color: var(--secondary-color);
  border-bottom: 1px solid var(--light-gray);
}

.BuyerBids .table-row {
  display: grid;
  grid-template-columns: 0.5fr 1fr 3fr 1.5fr 1fr 1fr 0.5fr;
  padding: var(--smallfont) var(--basefont);
  border-bottom: 1px solid var(--light-gray);
  align-items: center;
}

.BuyerBids .table-row:last-child {
  border-bottom: none;
}

.BuyerBids .table-cell {
  padding: 0 var(--extrasmallfont);
  font-size: var(--smallfont);
  text-align: center; /* Center all content as requested */
}

.BuyerBids .content-item {
  display: flex;
  align-items: center;
  gap: var(--smallfont);
  
}

.BuyerBids .content-image {
  width: 50px;
  height: 50px;
  border-radius: var(--border-radius);
  overflow: hidden;
  flex-shrink: 0;
}

.BuyerBids .content-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.BuyerBids .content-info {
  display: flex;
  flex-direction: column;
  text-align: left; /* Keep text left-aligned within the centered container */
}

.BuyerBids .content-title {
  font-weight: 500;
  color: var(--text-color);
  margin-bottom: 2px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 200px;
}

.BuyerBids .content-coach {
  font-size: var(--extrasmallfont);
  color: var(--dark-gray);
}

.BuyerBids .status-badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: var(--border-radius);
  font-size: var(--extrasmallfont);
  font-weight: 500;
  text-align: center;
}

.BuyerBids .status-badge.active {
  background-color: rgba(52, 152, 219, 0.1);
  color: #3498db;
}

.BuyerBids .status-badge.won {
  background-color: rgba(46, 204, 113, 0.1);
  color: #2ecc71;
}

.BuyerBids .status-badge.lost {
  background-color: rgba(231, 76, 60, 0.1);
  color: #e74c3c;
}

.BuyerBids .action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: transparent;
  color: var(--dark-gray);
  border: none;
  cursor: pointer;
  transition: color 0.3s ease;
  font-size: var(--basefont);
}

.BuyerBids .action-btn:hover {
  color: var(--btn-color);
}

.BuyerBids__empty {
  text-align: center;
  padding: var(--heading4);
  color: var(--dark-gray);
}

.BuyerBids__empty p {
  font-size: var(--basefont);
}

/* Responsive styles */
@media (max-width: 992px) {
  .BuyerBids .table-header,
  .BuyerBids .table-row {
    grid-template-columns: 0.5fr 1fr 2fr 1.5fr 1fr 1fr 0.5fr;
  }

  .BuyerBids .content-title {
    max-width: 150px;
  }
}

@media (max-width: 768px) {
  .BuyerBids .table {
    overflow-x: auto;
  }

  .BuyerBids .table-header,
  .BuyerBids .table-row {
    min-width: 700px;
  }
}
