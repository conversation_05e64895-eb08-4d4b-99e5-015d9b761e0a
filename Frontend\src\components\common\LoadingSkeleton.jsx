import React from 'react';
import '../../styles/LoadingSkeleton.css';

const LoadingSkeleton = ({ 
  variant = 'rectangular', 
  width = '100%', 
  height = '20px',
  className = '',
  count = 1,
  animation = 'pulse'
}) => {
  const skeletons = Array.from({ length: count }, (_, index) => (
    <div
      key={index}
      className={`skeleton skeleton--${variant} skeleton--${animation} ${className}`}
      style={{ width, height }}
    />
  ));

  return count === 1 ? skeletons[0] : <div className="skeleton-group">{skeletons}</div>;
};

// Predefined skeleton components for common use cases
export const TableRowSkeleton = ({ columns = 7 }) => (
  <div className="table-row-skeleton">
    {Array.from({ length: columns }, (_, index) => (
      <LoadingSkeleton key={index} height="40px" className="table-cell-skeleton" />
    ))}
  </div>
);

export const CardSkeleton = () => (
  <div className="card-skeleton">
    <LoadingSkeleton variant="rectangular" height="200px" className="card-image-skeleton" />
    <div className="card-content-skeleton">
      <LoadingSkeleton height="20px" width="80%" className="card-title-skeleton" />
      <LoadingSkeleton height="16px" width="60%" className="card-subtitle-skeleton" />
      <LoadingSkeleton height="24px" width="40%" className="card-price-skeleton" />
    </div>
  </div>
);

export const StatCardSkeleton = () => (
  <div className="stat-card-skeleton">
    <LoadingSkeleton variant="circular" width="60px" height="60px" className="stat-icon-skeleton" />
    <div className="stat-content-skeleton">
      <LoadingSkeleton height="32px" width="60px" className="stat-number-skeleton" />
      <LoadingSkeleton height="16px" width="100px" className="stat-label-skeleton" />
    </div>
  </div>
);

export const DashboardSkeleton = () => (
  <div className="dashboard-skeleton">
    {/* Stats Cards Skeleton */}
    <div className="stats-skeleton">
      <StatCardSkeleton />
      <StatCardSkeleton />
      <StatCardSkeleton />
    </div>

    {/* Section Skeleton */}
    <div className="section-skeleton">
      <div className="section-header-skeleton">
        <LoadingSkeleton height="24px" width="200px" />
        <LoadingSkeleton height="16px" width="100px" />
      </div>
      <div className="table-skeleton">
        <TableRowSkeleton />
        <TableRowSkeleton />
      </div>
    </div>

    {/* Another Section Skeleton */}
    <div className="section-skeleton">
      <div className="section-header-skeleton">
        <LoadingSkeleton height="24px" width="180px" />
        <LoadingSkeleton height="16px" width="120px" />
      </div>
      <div className="table-skeleton">
        <TableRowSkeleton />
        <TableRowSkeleton />
      </div>
    </div>
  </div>
);

export const StrategiesGridSkeleton = ({ count = 8 }) => (
  <div className="strategies-grid-skeleton">
    {Array.from({ length: count }, (_, index) => (
      <CardSkeleton key={index} />
    ))}
  </div>
);

export default LoadingSkeleton;
