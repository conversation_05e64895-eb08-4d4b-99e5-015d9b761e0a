/**
 * Utility functions for seller-related operations
 */

/**
 * Check if seller needs to complete onboarding
 * @param {Object} user - User object from Redux state or localStorage
 * @returns {boolean} True if seller needs onboarding, false otherwise
 */
export const needsSellerOnboarding = (user) => {
  if (!user) {
    return false;
  }

  // Check if user is currently in seller mode (either role=seller or activeRole=seller)
  const effectiveRole = user.role === 'admin' ? user.role : (user.activeRole || user.role);

  // Only check for users in seller mode
  if (effectiveRole !== 'seller') {
    return false;
  }

  // Check if onboarding is complete
  const sellerInfo = user.sellerInfo || {};
  return !sellerInfo.isOnboardingComplete;
};

/**
 * Get the appropriate redirect path for a seller based on onboarding status
 * @param {Object} user - User object from Redux state or localStorage
 * @returns {string} Redirect path
 */
export const getSellerRedirectPath = (user) => {
  if (!user) {
    return '/seller/dashboard';
  }

  // Check if user is currently in seller mode
  const effectiveRole = user.role === 'admin' ? user.role : (user.activeRole || user.role);

  if (effectiveRole !== 'seller') {
    return '/seller/dashboard';
  }

  // If onboarding is not complete, redirect to onboarding
  if (needsSellerOnboarding(user)) {
    return '/seller-onboarding';
  }

  // Otherwise, redirect to dashboard
  return '/seller/dashboard';
};

/**
 * Check if current route requires seller onboarding to be complete
 * @param {string} pathname - Current route pathname
 * @returns {boolean} True if route requires completed onboarding
 */
export const requiresOnboardingComplete = (pathname) => {
  // Routes that require onboarding to be complete
  const protectedSellerRoutes = [
    '/seller/dashboard',
    '/seller/profile',
    '/seller/requests',
    '/seller/earnings',
    '/seller/settings'
  ];

  return protectedSellerRoutes.some(route => pathname.startsWith(route));
};

/**
 * Check if user is currently in seller mode (for dual role users)
 * @param {Object} user - User object from Redux state or localStorage
 * @returns {boolean} True if user is currently in seller mode
 */
export const isInSellerMode = (user) => {
  if (!user) {
    return false;
  }

  const effectiveRole = user.role === 'admin' ? user.role : (user.activeRole || user.role);
  return effectiveRole === 'seller';
};

/**
 * Get the effective role for a user (handles dual role users)
 * @param {Object} user - User object from Redux state or localStorage
 * @returns {string} The effective role ('buyer', 'seller', or 'admin')
 */
export const getEffectiveRole = (user) => {
  if (!user) {
    return null;
  }

  return user.role === 'admin' ? user.role : (user.activeRole || user.role);
};
