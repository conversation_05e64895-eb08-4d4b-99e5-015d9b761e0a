import React, { useEffect, useState, useRef } from "react";
import { useSelector, useDispatch } from "react-redux";
import {
  selectCards,
  selectCardViewMode,
  selectCardsLoading,
  selectCardsError,
  fetchCards,
  removeCard,
  setCardViewMode,
  setDefaultCard,
  reset,
  createCard,
} from "../../redux/slices/cardSlice";
import SellerLayout from "../../components/seller/SellerLayout";
import stripePromise from "../../utils/stripe";
import { showSuccess, showError, showLoading, updateToast } from "../../utils/toast";
import {
  FaPlus,
  FaTrash,
  FaCreditCard,
  FaStar,
  FaRegStar,
 
} from "react-icons/fa";
import "../../styles/SellerCards.css";

const SellerCards = () => {
  const dispatch = useDispatch();
  const cards = useSelector(selectCards);
  const viewMode = useSelector(selectCardViewMode);
  const isLoading = useSelector(selectCardsLoading);
  const error = useSelector(selectCardsError);

  // Form state
  const [cardForm, setCardForm] = useState({
    nameOnCard: '',
    cardNumber: '',
    expiryDate: '',
    cvv: '',
  });
  const [isProcessing, setIsProcessing] = useState(false);
  const [stripe, setStripe] = useState(null);
  const [elements, setElements] = useState(null);
  const [cardElement, setCardElement] = useState(null);
  const cardElementRef = useRef(null);

  // Initialize Stripe
  useEffect(() => {
    const initializeStripe = async () => {
      try {
        const stripeInstance = await stripePromise;
        if (!stripeInstance) {
          throw new Error('Stripe failed to load');
        }

        setStripe(stripeInstance);

        const elementsInstance = stripeInstance.elements();
        setElements(elementsInstance);

        const cardElementInstance = elementsInstance.create('card', {
          style: {
            base: {
              color: '#424770',
              fontFamily: '"Helvetica Neue", Helvetica, sans-serif',
              fontSmoothing: 'antialiased',
              fontSize: '16px',
              '::placeholder': {
                color: '#aab7c4',
              },
            },
            invalid: {
              color: '#9e2146',
              iconColor: '#9e2146',
            },
          },
          hidePostalCode: true,
        });

        setCardElement(cardElementInstance);
      } catch (error) {
        console.error('Error initializing Stripe:', error);
        showError('Failed to load payment form. Please refresh the page.');
      }
    };

    if (viewMode === 'add') {
      initializeStripe();
    }

    // Cleanup
    return () => {
      if (cardElement) {
        cardElement.unmount();
      }
    };
  }, [viewMode]);

  // Mount card element when ref is available
  useEffect(() => {
    if (cardElement && cardElementRef.current && viewMode === 'add') {
      cardElement.mount(cardElementRef.current);
    }
  }, [cardElement, viewMode]);

  // Fetch cards on component mount
  useEffect(() => {
    dispatch(fetchCards());

    // Cleanup on unmount
    return () => {
      dispatch(reset());
    };
  }, [dispatch]);

  // Handle error display
  useEffect(() => {
    if (error) {
      showError(error.message || 'Failed to load cards');
      dispatch(reset());
    }
  }, [error, dispatch]);

  // Toggle between list and add views
  const toggleAddCardView = () => {
    if (viewMode === "add") {
      setCardForm({
        nameOnCard: '',
        cardNumber: '',
        expiryDate: '',
        cvv: '',
      });
    }
    dispatch(setCardViewMode(viewMode === "list" ? "add" : "list"));
  };

  // Handle card deletion
  const handleDeleteCard = async (cardId) => {
    if (window.confirm("Are you sure you want to remove this card?")) {
      try {
        await dispatch(removeCard(cardId)).unwrap();
        showSuccess('Card removed successfully');
      } catch (error) {
        showError(error.message || 'Failed to remove card');
      }
    }
  };

  // Handle setting default card
  const handleSetDefault = async (cardId) => {
    try {
      await dispatch(setDefaultCard(cardId)).unwrap();
      showSuccess('Default card updated');
    } catch (error) {
      showError(error.message || 'Failed to update default card');
    }
  };

  // Handle form input changes
  const handleNameChange = (value) => {
    setCardForm(prev => ({ ...prev, nameOnCard: value }));
  };

  const handleCardNumberChange = (value) => {
    // Remove all non-digits and limit to 16 digits
    const cleanValue = value.replace(/\D/g, "").slice(0, 16);
    // Format with spaces every 4 digits
    const formattedValue = cleanValue.replace(/(\d{4})(?=\d)/g, "$1 ");
    setCardForm(prev => ({ ...prev, cardNumber: formattedValue }));
  };

  const handleExpiryChange = (value) => {
    // Remove all non-digits
    const cleanValue = value.replace(/\D/g, "");
    // Format as MM/YY
    let formattedValue = cleanValue;
    if (cleanValue.length >= 2) {
      formattedValue = cleanValue.slice(0, 2) + "/" + cleanValue.slice(2, 4);
    }
    setCardForm(prev => ({ ...prev, expiryDate: formattedValue }));
  };

  const handleCvvChange = (value) => {
    setCardForm(prev => ({ ...prev, cvv: value }));
  };

  // Handle form submission with Stripe
  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!cardForm.nameOnCard.trim()) {
      showError('Please enter cardholder name');
      return;
    }

    if (!stripe || !cardElement) {
      showError('Payment form not ready. Please try again.');
      return;
    }

    setIsProcessing(true);
    const toastId = showLoading('Adding your card...');

    try {
      // Create payment method with Stripe
      const { error, paymentMethod } = await stripe.createPaymentMethod({
        type: 'card',
        card: cardElement,
        billing_details: {
          name: cardForm.nameOnCard.trim(),
        },
      });

      if (error) {
        throw new Error(error.message);
      }

      // Add card to backend
      await dispatch(createCard({
        paymentMethodId: paymentMethod.id,
        isDefault: cards.length === 0 // Make first card default
      })).unwrap();

      updateToast(toastId, 'Card added successfully!', 'success');

      // Reset form and switch to list view
      setCardForm({
        nameOnCard: '',
        cardNumber: '',
        expiryDate: '',
        cvv: '',
      });
      dispatch(setCardViewMode('list'));

    } catch (error) {
      console.error('Error adding card:', error);
      updateToast(toastId, error.message || 'Failed to add card', 'error');
    } finally {
      setIsProcessing(false);
    }
  };

  // Get card type logo
  const getCardLogo = (cardType) => {
    const logos = {
      visa: "https://upload.wikimedia.org/wikipedia/commons/thumb/5/5e/Visa_Inc._logo.svg/200px-Visa_Inc._logo.svg.png",
      mastercard: "https://upload.wikimedia.org/wikipedia/commons/thumb/2/2a/Mastercard-logo.svg/200px-Mastercard-logo.svg.png",
      amex: "https://upload.wikimedia.org/wikipedia/commons/thumb/f/fa/American_Express_logo_%282018%29.svg/200px-American_Express_logo_%282018%29.svg.png",
      discover: "https://upload.wikimedia.org/wikipedia/commons/thumb/5/57/Discover_Card_logo.svg/200px-Discover_Card_logo.svg.png",
    };
    return logos[cardType] || logos.mastercard;
  };

  if (isLoading) {
    return (
      <SellerLayout>
        <div className="SellerCards">
          <div className="SellerCards__loading">Loading cards...</div>
        </div>
      </SellerLayout>
    );
  }

  return (
    <SellerLayout>
      <div className="SellerCards">
        {viewMode === "list" ? (
          <div className="SellerCards__list-view">
            <div className="SellerCards__header">
              <h3 className="SellerCards__subtitle">Saved Cards</h3>
              <button
                className="btn-outline selleraddcard"
                onClick={toggleAddCardView}
              >
                <FaPlus /> Add New Card
              </button>
            </div>

            <div className="SellerCards__cards-list">
              {cards.length > 0 ? (
                cards.map((card) => (
                  <div className="SellerCards__card-item" key={card._id}>
                    <div className="SellerCards__card-content">
                      <div className="SellerCards__card-info">
                        <div className="SellerCards__card-logo">
                          <img
                            src={getCardLogo(card.cardType)}
                            alt={card.cardType}
                          />
                        </div>
                        <div className="SellerCards__card-details">
                          <div className="SellerCards__card-number">
                            •••• •••• •••• {card.lastFourDigits}
                          </div>
                          <div className="SellerCards__card-name">
                            {card.cardholderName}
                          </div>
                          <div className="SellerCards__card-expiry">
                            Expires {card.expiryMonth.toString().padStart(2, '0')}/{card.expiryYear.toString().slice(-2)}
                          </div>
                        </div>
                      </div>
                      <div className="SellerCards__card-actions">
                        <button
                          className={`SellerCards__default-btn ${card.isDefault ? 'active' : ''}`}
                          onClick={() => !card.isDefault && handleSetDefault(card._id)}
                          disabled={card.isDefault}
                          title={card.isDefault ? 'Default card' : 'Set as default'}
                        >
                          {card.isDefault ? <FaStar /> : <FaRegStar />}
                        </button>
                        <button
                          className="SellerCards__delete-btn"
                          onClick={() => handleDeleteCard(card._id)}
                          aria-label="Delete card"
                        >
                          <FaTrash />
                        </button>
                      </div>
                    </div>
                  </div>
                ))
              ) : (
                <div className="SellerCards__empty-state">
                  <FaCreditCard className="SellerCards__empty-icon" />
                  <p>You have no saved payment methods yet.</p>
                  <button
                    className="SellerCards__add-first-btn btn-primary"
                    onClick={toggleAddCardView}
                  >
                    Add Your First Card
                  </button>
                </div>
              )}
            </div>
          </div>
        ) : (
          <div className="SellerCards__add-view">
            <div className="SellerCards__header">
              <h3 className="SellerCards__subtitle">Add New Card</h3>
            </div>

            <div className="SellerCards__form">
              <form onSubmit={handleSubmit}>
                <div className="SellerCards__form-row">
                  <div className="SellerCards__input-field SellerCards__input-field--full">
                    <div className="SellerCards__input-container">
                      <div className="SellerCards__input-icon">
                     
                      </div>
                      <input
                        type="text"
                        id="nameOnCard"
                        name="nameOnCard"
                        value={cardForm.nameOnCard}
                        onChange={(e) => handleNameChange(e.target.value)}
                        placeholder="Name on card"
                        required
                        className="SellerCards__input"
                        disabled={isProcessing}
                      />
                    </div>
                  </div>
                </div>

                <div className="SellerCards__form-row">
                  <div className="SellerCards__input-field SellerCards__input-field--full">
                    <div className="SellerCards__input-container">
                      <div className="SellerCards__input-icon">
                  
                      </div>
                      <div
                        ref={cardElementRef}
                        className="SellerCards__stripe-element"
                    
                      />
                    </div>
                  </div>
                </div>

                <div className="SellerCards__form-actions">
                  <button
                    type="submit"
                    className="btn-primary"
                    disabled={isProcessing || !stripe}
                  >
                    {isProcessing ? 'Adding Card...' : 'Add Card'}
                  </button>
                  <button
                className="SellerCards__cancel-btn"
                onClick={toggleAddCardView}
                disabled={isProcessing}
              >
                Cancel
              </button>
                </div>
              </form>
            </div>

            
          </div>
        )}
      </div>
    </SellerLayout>
  );
};

export default SellerCards;
