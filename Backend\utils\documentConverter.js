const fs = require('fs');
const path = require('path');
const mammoth = require('mammoth');
const XLSX = require('xlsx');

/**
 * Custom Document Converter Service
 * Converts Office documents to HTML/JSON for preview without external services
 */

/**
 * Convert Word document to HTML
 * @param {string} filePath - Path to the Word document
 * @param {boolean} firstPageOnly - Whether to extract only the first page content
 * @returns {Promise<Object>} - Converted HTML content and metadata
 */
const convertWordToHtml = async (filePath, firstPageOnly = false) => {
  try {
    console.log(`[DocumentConverter] Converting Word document: ${filePath} (firstPageOnly: ${firstPageOnly})`);

    // Check if file exists
    if (!fs.existsSync(filePath)) {
      throw new Error(`Word document not found: ${filePath}`);
    }

    // Convert using mammoth
    const result = await mammoth.convertToHtml({ path: filePath });

    // Extract text content for metadata
    const textResult = await mammoth.extractRawText({ path: filePath });

    let htmlContent = result.value;
    let textContent = textResult.value;

    // If first page only, truncate content
    if (firstPageOnly) {
      // Split HTML by paragraphs and take first portion (approximate first page)
      const paragraphs = htmlContent.split('</p>');
      const firstPageParagraphs = paragraphs.slice(0, Math.min(10, paragraphs.length)); // Limit to ~10 paragraphs
      htmlContent = firstPageParagraphs.join('</p>') + (firstPageParagraphs.length < paragraphs.length ? '</p>' : '');

      // Add preview notice
      if (firstPageParagraphs.length < paragraphs.length) {
        htmlContent += '<div style="margin-top: 20px; padding: 10px; background-color: #f0f0f0; border-left: 4px solid #007bff; font-style: italic;">This is a preview showing the first page. Download the full document to view all content.</div>';
      }

      // Truncate text content similarly
      const textParagraphs = textContent.split('\n\n');
      textContent = textParagraphs.slice(0, Math.min(10, textParagraphs.length)).join('\n\n');
    }

    const wordCount = textContent.split(/\s+/).filter(word => word.length > 0).length;
    const charCount = textContent.length;

    console.log(`[DocumentConverter] Word conversion successful. Words: ${wordCount}, Characters: ${charCount}, FirstPageOnly: ${firstPageOnly}`);

    return {
      success: true,
      html: htmlContent,
      text: textContent,
      metadata: {
        wordCount,
        charCount,
        hasImages: htmlContent.includes('<img'),
        warnings: result.messages || [],
        isPreview: firstPageOnly
      }
    };
  } catch (error) {
    console.error(`[DocumentConverter] Word conversion failed:`, error);
    return {
      success: false,
      error: error.message,
      html: null,
      text: null,
      metadata: null
    };
  }
};

/**
 * Convert Excel document to structured data
 * @param {string} filePath - Path to the Excel document
 * @param {boolean} firstSheetOnly - Whether to extract only the first sheet content
 * @returns {Promise<Object>} - Converted data and metadata
 */
const convertExcelToData = async (filePath, firstSheetOnly = false) => {
  try {
    console.log(`[DocumentConverter] Converting Excel document: ${filePath} (firstSheetOnly: ${firstSheetOnly})`);

    // Check if file exists
    if (!fs.existsSync(filePath)) {
      throw new Error(`Excel document not found: ${filePath}`);
    }

    // Read the workbook
    const workbook = XLSX.readFile(filePath);
    const sheetNames = workbook.SheetNames;

    const sheets = {};
    let totalRows = 0;
    let totalCells = 0;

    // Determine which sheets to process
    const sheetsToProcess = firstSheetOnly ? [sheetNames[0]] : sheetNames;

    // Convert each sheet
    sheetsToProcess.forEach(sheetName => {
      const worksheet = workbook.Sheets[sheetName];
      const jsonData = XLSX.utils.sheet_to_json(worksheet, {
        header: 1,
        defval: '',
        raw: false
      });

      // Filter out empty rows
      const filteredData = jsonData.filter(row =>
        row.some(cell => cell !== null && cell !== undefined && cell !== '')
      );

      // Limit rows for preview (first sheet gets more rows if it's the only one)
      const maxRows = firstSheetOnly ? 20 : 100;
      const previewData = filteredData.slice(0, maxRows);

      sheets[sheetName] = {
        data: previewData,
        rowCount: filteredData.length,
        columnCount: Math.max(...filteredData.map(row => row.length), 0),
        isPreview: firstSheetOnly && filteredData.length > maxRows
      };

      totalRows += filteredData.length;
      totalCells += filteredData.reduce((sum, row) => sum + row.length, 0);
    });

    console.log(`[DocumentConverter] Excel conversion successful. Sheets processed: ${sheetsToProcess.length}/${sheetNames.length}, Total rows: ${totalRows}, FirstSheetOnly: ${firstSheetOnly}`);

    return {
      success: true,
      sheets,
      metadata: {
        sheetCount: sheetNames.length,
        sheetNames,
        totalRows,
        totalCells,
        hasFormulas: false, // Could be enhanced to detect formulas
        isPreview: firstSheetOnly,
        processedSheets: sheetsToProcess.length
      }
    };
  } catch (error) {
    console.error(`[DocumentConverter] Excel conversion failed:`, error);
    return {
      success: false,
      error: error.message,
      sheets: null,
      metadata: null
    };
  }
};

/**
 * Convert PowerPoint document to structured data
 * Note: PowerPoint conversion is limited without specialized libraries
 * This provides basic metadata and structure information
 * @param {string} filePath - Path to the PowerPoint document
 * @param {boolean} firstSlideOnly - Whether to extract only the first slide content
 * @returns {Promise<Object>} - Basic document information
 */
const convertPowerPointToData = async (filePath, firstSlideOnly = false) => {
  try {
    console.log(`[DocumentConverter] Analyzing PowerPoint document: ${filePath} (firstSlideOnly: ${firstSlideOnly})`);

    // Check if file exists
    if (!fs.existsSync(filePath)) {
      throw new Error(`PowerPoint document not found: ${filePath}`);
    }

    // Get file stats
    const stats = fs.statSync(filePath);
    const fileSize = stats.size;

    // For now, we'll provide basic file information
    // In a full implementation, you might use libraries like 'officegen' or 'node-pptx'
    // or implement a custom PPTX parser

    console.log(`[DocumentConverter] PowerPoint analysis complete. Size: ${fileSize} bytes, FirstSlideOnly: ${firstSlideOnly}`);

    // Create a more detailed preview structure
    const previewContent = {
      type: 'presentation',
      slides: [
        {
          slideNumber: 1,
          title: 'Slide 1',
          content: 'This is a preview of the first slide. The actual presentation contains more content.',
          notes: 'Download the full presentation to view all slides and content.'
        }
      ]
    };

    if (!firstSlideOnly) {
      // Add placeholder for additional slides
      previewContent.slides.push({
        slideNumber: 2,
        title: 'Additional Slides',
        content: 'This presentation may contain multiple slides with various content.',
        notes: 'Download to view all slides.'
      });
    }

    return {
      success: true,
      metadata: {
        fileSize,
        fileName: path.basename(filePath),
        fileType: 'PowerPoint Presentation',
        lastModified: stats.mtime,
        // Note: Slide count and content extraction would require specialized libraries
        slideCount: firstSlideOnly ? 1 : 'Multiple',
        hasImages: 'Unknown',
        hasAnimations: 'Unknown',
        isPreview: firstSlideOnly
      },
      preview: previewContent
    };
  } catch (error) {
    console.error(`[DocumentConverter] PowerPoint analysis failed:`, error);
    return {
      success: false,
      error: error.message,
      metadata: null,
      preview: null
    };
  }
};

/**
 * Main document conversion function
 * @param {string} filePath - Path to the document
 * @param {string} fileExtension - File extension (.docx, .xlsx, etc.)
 * @param {boolean} firstPageOnly - Whether to extract only the first page/sheet/slide
 * @returns {Promise<Object>} - Conversion result
 */
const convertDocument = async (filePath, fileExtension, firstPageOnly = false) => {
  const ext = fileExtension.toLowerCase();

  console.log(`[DocumentConverter] Starting conversion for ${ext} document: ${filePath} (firstPageOnly: ${firstPageOnly})`);

  try {
    switch (ext) {
      case '.doc':
      case '.docx':
      case '.docm':
      case '.dot':
      case '.dotx':
      case '.dotm':
        return await convertWordToHtml(filePath, firstPageOnly);

      case '.xls':
      case '.xlsx':
      case '.xlsm':
      case '.xlt':
      case '.xltx':
      case '.xltm':
      case '.csv':
        return await convertExcelToData(filePath, firstPageOnly);

      case '.ppt':
      case '.pptx':
      case '.pptm':
      case '.pot':
      case '.potx':
      case '.potm':
      case '.pps':
      case '.ppsx':
      case '.ppsm':
        return await convertPowerPointToData(filePath, firstPageOnly);

      default:
        console.log(`[DocumentConverter] Unsupported file type: ${ext}`);
        return {
          success: false,
          error: `Unsupported document type: ${ext}`,
          supportedTypes: ['.docx', '.xlsx', '.pptx', '.doc', '.xls', '.ppt']
        };
    }
  } catch (error) {
    console.error(`[DocumentConverter] Conversion failed for ${ext}:`, error);
    return {
      success: false,
      error: `Document conversion failed: ${error.message}`,
      filePath,
      fileExtension: ext
    };
  }
};

/**
 * Check if document type is supported for conversion
 * @param {string} fileExtension - File extension
 * @returns {boolean} - Whether the type is supported
 */
const isSupportedDocumentType = (fileExtension) => {
  const supportedTypes = [
    '.doc', '.docx', '.docm', '.dot', '.dotx', '.dotm',
    '.xls', '.xlsx', '.xlsm', '.xlt', '.xltx', '.xltm', '.csv',
    '.ppt', '.pptx', '.pptm', '.pot', '.potx', '.potm', '.pps', '.ppsx', '.ppsm'
  ];
  
  return supportedTypes.includes(fileExtension.toLowerCase());
};

/**
 * Get document type category
 * @param {string} fileExtension - File extension
 * @returns {string} - Document type (word, excel, powerpoint, unknown)
 */
const getDocumentType = (fileExtension) => {
  const ext = fileExtension.toLowerCase();
  
  if (['.doc', '.docx', '.docm', '.dot', '.dotx', '.dotm'].includes(ext)) {
    return 'word';
  }
  
  if (['.xls', '.xlsx', '.xlsm', '.xlt', '.xltx', '.xltm', '.csv'].includes(ext)) {
    return 'excel';
  }
  
  if (['.ppt', '.pptx', '.pptm', '.pot', '.potx', '.potm', '.pps', '.ppsx', '.ppsm'].includes(ext)) {
    return 'powerpoint';
  }
  
  return 'unknown';
};

/**
 * Generate preview for Word document (first page only)
 * @param {string} filePath - Path to the Word document
 * @returns {Promise<Object>} - Preview conversion result
 */
const generateWordPreview = async (filePath) => {
  return await convertWordToHtml(filePath, true);
};

/**
 * Generate preview for Excel document (first sheet only)
 * @param {string} filePath - Path to the Excel document
 * @returns {Promise<Object>} - Preview conversion result
 */
const generateExcelPreview = async (filePath) => {
  return await convertExcelToData(filePath, true);
};

/**
 * Generate preview for PowerPoint document (first slide only)
 * @param {string} filePath - Path to the PowerPoint document
 * @returns {Promise<Object>} - Preview conversion result
 */
const generatePowerPointPreview = async (filePath) => {
  return await convertPowerPointToData(filePath, true);
};

/**
 * Generate document preview based on file extension
 * @param {string} filePath - Path to the document
 * @param {string} fileExtension - File extension
 * @returns {Promise<Object>} - Preview conversion result
 */
const generateDocumentPreview = async (filePath, fileExtension) => {
  return await convertDocument(filePath, fileExtension, true);
};

module.exports = {
  convertDocument,
  convertWordToHtml,
  convertExcelToData,
  convertPowerPointToData,
  generateWordPreview,
  generateExcelPreview,
  generatePowerPointPreview,
  generateDocumentPreview,
  isSupportedDocumentType,
  getDocumentType
};
