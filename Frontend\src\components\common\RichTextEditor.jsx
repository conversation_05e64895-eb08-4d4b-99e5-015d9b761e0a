import React, { Component } from 'react';
import SummernoteEditor from './SummernoteEditor';

// Import bootstrap dependencies (already imported in SummernoteEditor)
import 'bootstrap/js/modal';
import 'bootstrap/js/dropdown';
import 'bootstrap/js/tooltip';

class RichTextEditor extends Component {
  constructor(props) {
    super(props);
    this.state = {
      content: this.props.defaultValue || 'Default value'
    };
  }

  onChange = (content) => {
    console.log('onChange', content);
    this.setState({ content });
    
    // Call parent onChange if provided
    if (this.props.onChange) {
      this.props.onChange(content);
    }
  }

  render() {
    const { 
      height = 350, 
      placeholder = 'Enter text here...',
      className = '',
      disabled = false,
      lang = 'en-US'
    } = this.props;

    const summernoteOptions = {
      lang: lang,
      height: height,
      dialogsInBody: true,
      toolbar: [
        ['style', ['style']],
        ['font', ['bold', 'underline', 'clear']],
        ['fontname', ['fontname']],
        ['para', ['ul', 'ol', 'paragraph']],
        ['table', ['table']],
        ['insert', ['link', 'picture', 'video']],
        ['view', ['fullscreen', 'codeview']]
      ]
    };

    return (
      <SummernoteEditor
        value={this.state.content}
        onChange={this.onChange}
        placeholder={placeholder}
        height={height}
        className={className}
        disabled={disabled}
        options={summernoteOptions}
      />
    );
  }
}

export default RichTextEditor;
