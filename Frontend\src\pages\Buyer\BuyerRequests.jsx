import React, { useEffect } from "react";
import { useSelector, useDispatch } from "react-redux";
import {
  selectMyRequests,
  selectLoading,
  selectErrors,
  fetchBuyerRequests,
  clearError
} from "../../redux/slices/buyerDashboardSlice";
import SectionWrapper from "../../components/common/SectionWrapper";
import LoadingSkeleton, { TableRowSkeleton } from "../../components/common/LoadingSkeleton";
import { ErrorDisplay } from "../../components/common/ErrorBoundary";
import { FaEye, FaSync } from "react-icons/fa";
import Table from "../../components/common/Table";
import "../../styles/BuyerRequests.css";
import { MdRequestPage } from "react-icons/md";

const BuyerRequests = () => {
  const dispatch = useDispatch();
  const requests = useSelector(selectMyRequests);
  const loading = useSelector(selectLoading);
  const errors = useSelector(selectErrors);

  // Fetch requests on component mount
  useEffect(() => {
    dispatch(fetchBuyerRequests());
  }, [dispatch]);

  // Handle retry
  const handleRetry = () => {
    dispatch(clearError('requests'));
    dispatch(fetchBuyerRequests());
  };

  const columns = [
    {
      key: "no",
      label: "No.",
      className: "no",
    },
    {
      key: "requestId",
      label: "Request Id",
      className: "request-id",
    },
    {
      key: "video",
      label: "Videos/Documents",
      className: "video",
    },
    {
      key: "date",
      label: "Date",
      className: "date",
    },
    {
      key: "requestedAmount",
      label: "Requested Amount",
      className: "requested-amount",
    },
    {
      key: "status",
      label: "Status",
      className: "status",
    },
    {
      key: "action",
      label: "Action",
      className: "action",
    },
  ];

  const renderRow = (request, index) => (
    <>
      <div className="table-cell no">{index + 1}</div>
      <div className="table-cell request-id">#REQUEST{request.id}</div>
      <div className="table-cell video">
        <div className="content-item">
          <div className="content-image">
            <img
              src="https://images.unsplash.com/photo-1566577739112-5180d4bf9390?q=80&w=300&h=200&auto=format&fit=crop"
              alt={request.title}
            />
          </div>
          <div className="content-info">
            <div className="content-title">{request.title}</div>
            <div className="content-coach">By Coach</div>
          </div>
        </div>
      </div>
      <div className="table-cell date">{request.date} | 4:30PM</div>
      <div className="table-cell requested-amount">$22.00</div>
      <div className="table-cell status">
        <span className={`status-badge ${request.status}`}>
          {request.status.charAt(0).toUpperCase() + request.status.slice(1)}
        </span>
      </div>
      <div className="table-cell action">
        <button className="action-btn">
          <FaEye />
        </button>
      </div>
    </>
  );

  return (
    <div className="BuyerRequests">
      <SectionWrapper
        icon={<MdRequestPage className="BuyerSidebar__icon" />}
        title="My Requests"
        action={
          errors.requests && (
            <button
              className="retry-btn"
              onClick={handleRetry}
              title="Retry loading requests"
            >
              <FaSync />
            </button>
          )
        }
      >
        {errors.requests ? (
          <ErrorDisplay
            error={errors.requests}
            onRetry={handleRetry}
            title="Failed to load requests"
          />
        ) : loading.requests ? (
          <div className="loading-container">
            <TableRowSkeleton columns={7} />
            <TableRowSkeleton columns={7} />
            <TableRowSkeleton columns={7} />
          </div>
        ) : requests.length > 0 ? (
          <Table
            columns={columns}
            data={requests}
            renderRow={renderRow}
            variant="grid"
            gridTemplate="0.5fr 1fr 3fr 1.5fr 1.5fr 1fr 0.5fr"
            className="BuyerRequests__table"
            emptyMessage="You have no requests yet."
          />
        ) : (
          <div className="BuyerRequests__empty">
            <h3>No requests yet</h3>
            <p>You haven't made any custom content requests yet. Start by requesting specific training content from coaches.</p>
          </div>
        )}
      </SectionWrapper>
    </div>
  );
};

export default BuyerRequests;
