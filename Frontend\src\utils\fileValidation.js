/**
 * File validation utilities for content type-based file uploads
 */

// Define supported file extensions for each content type
export const FILE_TYPE_EXTENSIONS = {
  Video: [
    '.mp4', '.mov', '.avi', '.mkv', '.wmv', '.flv', '.webm', '.m4v', '.3gp',
    '.mpg', '.mpeg', '.m2v', '.m4v', '.3g2', '.asf', '.rm', '.rmvb', '.vob'
  ],
  Document: [
    // PDF documents
    '.pdf',
    // Microsoft Word documents
    '.doc', '.docx', '.docm', '.dot', '.dotx', '.dotm',
    // Microsoft Excel documents
    '.xls', '.xlsx', '.xlsm', '.xlt', '.xltx', '.xltm', '.csv',
    // Microsoft PowerPoint documents
    '.ppt', '.pptx', '.pptm', '.pot', '.potx', '.potm', '.pps', '.ppsx', '.ppsm',
    // Text documents
    '.txt', '.rtf',
    // OpenDocument formats
    '.odt', '.ods', '.odp', '.odg', '.odf',
    // Apple iWork documents
    '.pages', '.numbers', '.key',
    // Other document formats
    '.epub', '.mobi', '.azw', '.azw3'
  ]
};

// Define MIME types for each content type
export const FILE_TYPE_MIMES = {
  Video: [
    'video/mp4', 'video/quicktime', 'video/x-msvideo', 'video/x-matroska',
    'video/x-ms-wmv', 'video/x-flv', 'video/webm', 'video/3gpp', 'video/mpeg',
    'video/x-ms-asf', 'application/vnd.rn-realmedia', 'video/vnd.rn-realvideo'
  ],
  Document: [
    // PDF documents
    'application/pdf',
    // Microsoft Word documents
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/vnd.ms-word.document.macroEnabled.12',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.template',
    'application/vnd.ms-word.template.macroEnabled.12',
    // Microsoft Excel documents
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'application/vnd.ms-excel.sheet.macroEnabled.12',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.template',
    'application/vnd.ms-excel.template.macroEnabled.12',
    'text/csv',
    // Microsoft PowerPoint documents
    'application/vnd.ms-powerpoint',
    'application/vnd.openxmlformats-officedocument.presentationml.presentation',
    'application/vnd.ms-powerpoint.presentation.macroEnabled.12',
    'application/vnd.openxmlformats-officedocument.presentationml.template',
    'application/vnd.ms-powerpoint.template.macroEnabled.12',
    'application/vnd.openxmlformats-officedocument.presentationml.slideshow',
    'application/vnd.ms-powerpoint.slideshow.macroEnabled.12',
    // Text documents
    'text/plain',
    'text/rtf',
    'application/rtf',
    // OpenDocument formats
    'application/vnd.oasis.opendocument.text',
    'application/vnd.oasis.opendocument.spreadsheet',
    'application/vnd.oasis.opendocument.presentation',
    'application/vnd.oasis.opendocument.graphics',
    'application/vnd.oasis.opendocument.formula',
    // Apple iWork documents
    'application/vnd.apple.pages',
    'application/vnd.apple.numbers',
    'application/vnd.apple.keynote',
    // E-book formats
    'application/epub+zip',
    'application/x-mobipocket-ebook',
    'application/vnd.amazon.ebook'
  ]
};

/**
 * Get file extension from filename
 * @param {string} filename - The filename
 * @returns {string} - The file extension (lowercase with dot)
 */
export const getFileExtension = (filename) => {
  if (!filename) return '';
  const lastDotIndex = filename.lastIndexOf('.');
  if (lastDotIndex === -1) return '';
  return filename.substring(lastDotIndex).toLowerCase();
};

/**
 * Validate file based on selected content type
 * @param {File} file - The file object
 * @param {string} contentType - The selected content type ('Video' or 'Document')
 * @returns {Object} - Validation result with isValid and message
 */
export const validateFileByContentType = (file, contentType) => {
  if (!file) {
    return {
      isValid: false,
      message: 'No file selected'
    };
  }

  if (!contentType) {
    return {
      isValid: false,
      message: 'Please select a content type first'
    };
  }

  const fileExtension = getFileExtension(file.name);
  const allowedExtensions = FILE_TYPE_EXTENSIONS[contentType];
  const allowedMimes = FILE_TYPE_MIMES[contentType];

  // Check file extension
  if (!allowedExtensions.includes(fileExtension)) {
    const extensionList = allowedExtensions.join(', ').toUpperCase();
    return {
      isValid: false,
      message: `Invalid file format for ${contentType}. Supported formats: ${extensionList}`
    };
  }

  // Check MIME type
  if (!allowedMimes.includes(file.type)) {
    return {
      isValid: false,
      message: `Invalid file type for ${contentType}. Please select a valid ${contentType.toLowerCase()} file.`
    };
  }

  // File size validation (500MB max)
  const maxSize = 500 * 1024 * 1024; // 500MB
  if (file.size > maxSize) {
    return {
      isValid: false,
      message: 'File size must be less than 500MB'
    };
  }

  return {
    isValid: true,
    message: 'File is valid'
  };
};

/**
 * Get accept attribute for file input based on content type
 * @param {string} contentType - The selected content type
 * @returns {string} - The accept attribute value
 */
export const getAcceptAttribute = (contentType) => {
  if (!contentType) return '';

  switch (contentType) {
    case 'Video':
      return 'video/*,.mp4,.mov,.avi,.mkv,.wmv,.flv,.webm,.m4v,.3gp';
    case 'Document':
      return '.pdf,.doc,.docx,.docm,.dot,.dotx,.dotm,.xls,.xlsx,.xlsm,.xlt,.xltx,.xltm,.csv,.ppt,.pptx,.pptm,.pot,.potx,.potm,.pps,.ppsx,.ppsm,.txt,.rtf,.odt,.ods,.odp,.odg,.odf,.pages,.numbers,.key,.epub,.mobi,.azw,.azw3';
    default:
      return '';
  }
};

/**
 * Check if file upload should be disabled
 * @param {string} contentType - The selected content type
 * @returns {boolean} - Whether file upload should be disabled
 */
export const isFileUploadDisabled = (contentType) => {
  return !contentType || (contentType !== 'Video' && contentType !== 'Document');
};

/**
 * Get placeholder text for file upload based on content type
 * @param {string} contentType - The selected content type
 * @returns {string} - The placeholder text
 */
export const getFileUploadPlaceholder = (contentType) => {
  if (!contentType) {
    return 'Please select a content type first';
  }

  switch (contentType) {
    case 'Video':
      return 'Drag & drop to upload video file';
    case 'Document':
      return 'Drag & drop to upload document file';
    default:
      return 'Drag & drop to upload file';
  }
};

export default {
  FILE_TYPE_EXTENSIONS,
  FILE_TYPE_MIMES,
  getFileExtension,
  validateFileByContentType,
  getAcceptAttribute,
  isFileUploadDisabled,
  getFileUploadPlaceholder
};
