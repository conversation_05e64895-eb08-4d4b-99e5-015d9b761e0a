/* SellerBids Component Styles */
.seller-bids-container {
  padding: var(--section-padding);
  background-color: var(--white);
  font-family: "Poppins", sans-serif;
  
  border-radius: var(--border-radius-large);
}

.bids-table {
  width: 100%;
  font-size: var(--basefont);
  background-color: var(--white);

  border-radius: var(--border-radius-large);
  
}

.bids-table th {
  padding: 12px 10px;
  text-align: left;
  vertical-align: middle;
}

.bids-table td {
  padding: 12px 10px;
  text-align: left;
  border-top: 1px solid var(--light-gray);
  vertical-align: middle;
}
.action-icon-container{
  display:flex;
  align-items:center;
  justify-content:center;
 
}

.video-doc {
  display: flex;
  align-items: center;
  gap: 10px;
}

.video-doc img {
  width: 55px;
  height: 55px;
  border-radius: var(--border-radius);
  object-fit: cover;
}

.video-doc span {
  font-size: var(--smallfont);
  font-weight: 500;
  color: var(--text-color);
}

.threedoticon {
font-size: var(--heading6);
  color: black;
  cursor: pointer;
  transition: all 0.2s ease;

  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: transparent;
}
.threedoticon:hover{
  color: var(--primary-color);
  background-color: var(--primary-light-color);
  transform: translateY(-2px);
}
.action-icon:hover {
  color: var(--primary-color);
  background-color: var(--primary-light-color);
  transform: translateY(-2px);
}
