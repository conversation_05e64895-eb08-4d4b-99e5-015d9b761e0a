/* Word Count Display Component Styles */
.word-count-display {




  font-size: var(--smallfont);
  transition: all 0.3s ease;
}

.word-count-info {
  display: flex;
  align-items: center;
  gap: var(--basefont);
  margin-bottom: var(--extrasmallfont);
  flex-wrap: wrap;
}

.word-count-text,
.char-count-text {
  color: var(--text-color);
  font-weight: 500;
}

.word-count-error-text {
  color: var(--btn-color);
  font-weight: 600;
  font-size: var(--extrasmallfont);
}

.word-count-warning-text {
  color: #f59e0b;
  font-weight: 500;
  font-size: var(--extrasmallfont);
}

.word-count-bar {
  width: 100%;
  height: 4px;
  background-color: var(--light-gray);
  border-radius: 2px;
  overflow: hidden;
}

.word-count-progress {
  height: 100%;
  background-color: var(--primary-color);
  border-radius: 2px;
  transition: width 0.3s ease, background-color 0.3s ease;
}

/* Status-based styling */


.word-count-normal .word-count-progress {
  background-color: var(--primary-color);
}

.word-count-warning {
  border-color: #f59e0b;
  background-color: rgba(245, 158, 11, 0.1);
}

.word-count-warning .word-count-progress {
  background-color: #f59e0b;
}

.word-count-error {
  border-color: var(--btn-color);
  background-color: rgba(238, 52, 37, 0.1);
}

.word-count-error .word-count-progress {
  background-color: var(--btn-color);
}

/* Responsive design */
@media (max-width: 768px) {
  .word-count-info {

    align-items: flex-start;
    gap: var(--extrasmallfont);
  }
  

}

@media (max-width: 480px) {
  .word-count-text,
  .char-count-text {
    font-size: var(--extrasmallfont);
  }
}
