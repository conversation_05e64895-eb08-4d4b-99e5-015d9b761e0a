/* Office Document Viewer Styles */
.office-document-viewer {
  display: flex;
  flex-direction: column;
  background-color: var(--white);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  overflow: hidden;
  position: relative;
}

/* Header */
.office-document-viewer__header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--smallfont) var(--basefont);
  background-color: var(--light-gray);
  border-bottom: 1px solid var(--border-color);
  min-height: 50px;
}

.office-document-viewer__info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.office-document-viewer__title {
  font-size: var(--smallfont);
  font-weight: 600;
  color: var(--secondary-color);
  line-height: 1.2;
}

.office-document-viewer__type {
  font-size: var(--tinyfont);
  color: var(--text-muted);
  line-height: 1.2;
}

.office-document-viewer__viewer {
  font-size: var(--tinyfont);
  color: var(--primary-color);
  font-style: italic;
  line-height: 1.2;
}

.office-document-viewer__download-btn {
  background: var(--primary-color);
  color: var(--white);
  border: none;
  border-radius: var(--border-radius);
  padding: var(--tinyfont) var(--smallfont);
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: var(--tinyfont);
  font-size: var(--smallfont);
  transition: all 0.3s ease;
  min-width: 40px;
  height: 36px;
  justify-content: center;
}

.office-document-viewer__download-btn:hover {
  background: var(--primary-dark);
  transform: translateY(-1px);
}

/* Content area */
.office-document-viewer__content {
  flex: 1;
  position: relative;
  min-height: 300px;
}

.office-document-viewer__iframe {
  width: 100%;
  height: 100%;
  border: none;
  background-color: var(--white);
}

/* Loading state */
.office-document-viewer__loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  min-height: 300px;
  color: var(--text-muted);
  background-color: var(--light-gray);
}

.office-document-viewer__loading .spinning {
  font-size: var(--heading5);
  margin-bottom: var(--basefont);
  animation: spin 1s linear infinite;
}

.office-document-viewer__loading p {
  margin: 0;
  font-size: var(--smallfont);
  text-align: center;
}

.office-document-viewer__retry-info {
  color: var(--primary-color) !important;
  font-size: var(--tinyfont) !important;
  margin-top: var(--tinyfont) !important;
}

/* Error state */
.office-document-viewer__error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  min-height: 300px;
  padding: var(--heading5);
  text-align: center;
  color: var(--text-muted);
  background-color: var(--light-gray);
}

.office-document-viewer__error svg {
  font-size: var(--heading3);
  color: var(--warning-color);
  margin-bottom: var(--basefont);
}

.office-document-viewer__error h3 {
  margin: 0 0 var(--smallfont) 0;
  font-size: var(--heading6);
  color: var(--secondary-color);
}

.office-document-viewer__error p {
  margin: 0 0 var(--tinyfont) 0;
  font-size: var(--smallfont);
  line-height: 1.4;
}

.office-document-viewer__download-button {
  background: var(--primary-color);
  color: var(--white);
  border: none;
  border-radius: var(--border-radius);
  padding: var(--smallfont) var(--basefont);
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: var(--tinyfont);
  font-size: var(--smallfont);
  margin-top: var(--basefont);
  transition: all 0.3s ease;
}

.office-document-viewer__download-button:hover {
  background: var(--primary-dark);
  transform: translateY(-1px);
}

/* Responsive design */
@media (max-width: 768px) {
  .office-document-viewer__header {
    padding: var(--tinyfont) var(--smallfont);
    min-height: 45px;
  }
  
  .office-document-viewer__title {
    font-size: var(--tinyfont);
  }
  
  .office-document-viewer__type,
  .office-document-viewer__viewer {
    font-size: 10px;
  }
  
  .office-document-viewer__download-btn {
    min-width: 35px;
    height: 32px;
    padding: var(--tinyfont);
  }
  
  .office-document-viewer__loading,
  .office-document-viewer__error {
    min-height: 250px;
    padding: var(--basefont);
  }
}

/* Animation for spinning icon */
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .office-document-viewer {
    background-color: var(--dark-bg, #1a1a1a);
    border-color: var(--dark-border, #333);
  }
  
  .office-document-viewer__header {
    background-color: var(--dark-header-bg, #2a2a2a);
    border-bottom-color: var(--dark-border, #333);
  }
  
  .office-document-viewer__loading,
  .office-document-viewer__error {
    background-color: var(--dark-content-bg, #2a2a2a);
  }
  
  .office-document-viewer__iframe {
    background-color: var(--dark-bg, #1a1a1a);
  }
}
