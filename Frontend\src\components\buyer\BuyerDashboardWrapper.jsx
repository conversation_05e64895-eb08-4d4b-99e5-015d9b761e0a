import React from 'react';
import ErrorBoundary from '../common/ErrorBoundary';
import { useDispatch } from 'react-redux';
import { clearAllErrors } from '../../redux/slices/buyerDashboardSlice';

const BuyerDashboardWrapper = ({ children }) => {
  const dispatch = useDispatch();

  const handleRetry = () => {
    // Clear all errors and reload the page
    dispatch(clearAllErrors());
    window.location.reload();
  };

  return (
    <ErrorBoundary
      title="Buyer Dashboard Error"
      message="Something went wrong with the buyer dashboard. Please try again."
      onRetry={handleRetry}
      retryText="Reload Dashboard"
      showDetails={process.env.NODE_ENV === 'development'}
    >
      {children}
    </ErrorBoundary>
  );
};

export default BuyerDashboardWrapper;
