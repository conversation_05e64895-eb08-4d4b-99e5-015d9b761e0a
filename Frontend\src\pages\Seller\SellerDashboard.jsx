import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import SellerLayout from "../../components/seller/SellerLayout";
import "../../styles/SellerDashboard.css";
import { FaFileAlt } from "react-icons/fa";
import { IoEyeSharp } from "react-icons/io5";
import { BsThreeDotsVertical } from "react-icons/bs";
import { LiaComment } from "react-icons/lia";
import { SlEye } from "react-icons/sl";
import { MdVideoLibrary } from "react-icons/md";
import { FaGavel } from "react-icons/fa";
import { MdRequestPage } from "react-icons/md";
import {
  fetchSellerDashboardStats,
  fetchSellerContent,
  fetchSellerRequests,
  fetchSellerBids,
  selectStats,
  selectMyContent,
  selectRequests,
  selectBids,
  selectLoading,
  selectErrors,
} from "../../redux/slices/sellerDashboardSlice";

// Helper function to format date
const formatDate = (dateString) => {
  if (!dateString) return 'N/A';
  const date = new Date(dateString);
  return date.toLocaleDateString('en-US', {
    day: '2-digit',
    month: 'short',
    year: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
    hour12: true
  });
};

// Helper function to format price
const formatPrice = (price) => {
  if (!price && price !== 0) return 'N/A';
  return `$${parseFloat(price).toFixed(2)}`;
};

const StatsCard = ({ count, label, color, icone }) => (
  <div className={`stats-card ${color}`}>
    <div className="detailmain">
      <h2>{count}</h2>
      <p>{label}</p>
    </div>
    <div className={`icon-round ${color}`}>{icone}</div>
  </div>
);

const StrategyRow = ({ item, toggle, onViewDetails }) => (
  <tr>
    <td>{item.id}</td>
    <td>
      <div className="video-title">
        <FaFileAlt className="video-icon" />
        <span>{item.title}</span>
      </div>
    </td>
    <td>{item.date}</td>
    <td>{item.price}</td>
    <td>
      <label className="switch">
        <input
          type="checkbox"
          checked={item.status}
          onChange={() => toggle(item.id)}
        />
        <span className="slider round"></span>
      </label>
    </td>
    <td>
      <div className="action-icons">
        <SlEye className="action-icon" onClick={() => onViewDetails(item.id)} />
      </div>
    </td>
  </tr>
);

const RequestRow = ({ item, showUser = true }) => (
  <tr>
    <td>{item.id}</td>
    <td>
      <div className="video-title">
        <FaFileAlt className="video-icon" />
        <span>{item.title}</span>
      </div>
    </td>
    <td>{item.date}</td>
    <td>{item.price}</td>
    <td>{item.amount}</td>
    {showUser ? <td>{item.user}</td> : null}
    <td>
      <div className="action-icons">
        <SlEye className="action-icon" />
        <LiaComment className="action-icon" />
      </div>
    </td>
  </tr>
);

const BidRow = ({ item }) => (
  <tr>
    <td>{item.id}</td>
    <td>
      <div className="video-title">
        <FaFileAlt className="video-icon" />
        <span>{item.title}</span>
      </div>
    </td>
    <td>{item.date}</td>
    <td>{item.price}</td>
    <td>{item.amount}</td>
    <td>
      <div className="action-icons">
        <BsThreeDotsVertical className="action-icon" />
      </div>
    </td>
  </tr>
);

const SellerDashboard = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();

  // Redux state
  const stats = useSelector(selectStats);
  const myContent = useSelector(selectMyContent);
  const requests = useSelector(selectRequests);
  const bids = useSelector(selectBids);
  const loading = useSelector(selectLoading);
  const errors = useSelector(selectErrors);

  // Local state for content status toggle
  const [strategies, setStrategies] = useState([]);

  // Fetch data on component mount
  useEffect(() => {
    dispatch(fetchSellerDashboardStats());
    dispatch(fetchSellerContent());
    dispatch(fetchSellerRequests());
    dispatch(fetchSellerBids());
  }, [dispatch]);

  // Update local strategies when myContent changes
  useEffect(() => {
    if (myContent && myContent.length > 0) {
      const formattedStrategies = myContent.map((content, index) => ({
        id: content._id || content.id || index + 1,
        title: content.title || 'Untitled Strategy',
        date: formatDate(content.createdAt || content.date),
        price: formatPrice(content.price),
        status: content.isActive !== undefined ? content.isActive : true,
      }));
      setStrategies(formattedStrategies);
    }
  }, [myContent]);

  // Create dynamic stats array
  const dynamicStats = [
    {
      count: String(stats.totalStrategies || myContent?.length || 0).padStart(2, '0'),
      label: "Total Strategies",
      color: "purple",
      icone: <MdVideoLibrary/>,
    },
    {
      count: String(stats.totalRequests || requests?.length || 0).padStart(2, '0'),
      label: "Requests",
      color: "orange",
      icone: <MdRequestPage />
    },
    {
      count: String(stats.totalBids || bids?.length || 0).padStart(2, '0'),
      label: "Bids",
      color: "green",
      icone: <FaGavel/>
    },
  ];

  const toggleStatus = (id) => {
    setStrategies((prev) =>
      prev.map((item) =>
        item.id === id ? { ...item, status: !item.status } : item
      )
    );
  };

  const handleViewDetails = (id) => {
    navigate(`/seller/strategy-details/${id}`);
  };

  // Format requests data for display
  const formattedRequests = requests?.slice(0, 2).map((request, index) => ({
    id: request._id || `#${index + 1}`,
    title: request.title || 'Untitled Request',
    date: formatDate(request.createdAt || request.date),
    price: formatPrice(request.budget || request.price),
    amount: formatPrice(request.sellerResponse?.price || request.requestedAmount),
    user: request.buyer ? `${request.buyer.firstName || ''} ${request.buyer.lastName || ''}`.trim() || 'Unknown User' : 'Unknown User',
  })) || [];

  // Format bids data for display
  const formattedBids = bids?.slice(0, 2).map((bid, index) => ({
    id: bid._id || `#${index + 1}`,
    title: bid.content?.title || 'Untitled Content',
    date: formatDate(bid.createdAt || bid.date),
    price: formatPrice(bid.content?.price || bid.price),
    amount: formatPrice(bid.amount || bid.bidAmount),
  })) || [];

  return (
    <SellerLayout>
      <div className="dashboard-container">
        <div className="stats-container">
          {dynamicStats.map((stat, idx) => (
            <StatsCard key={idx} {...stat} />
          ))}
        </div>

        <div className="section">
          <div className="section-header">
            <h3>My Sports Strategies</h3>
            <a href="/downloads">View All Downloads</a>
          </div>
          <div className="table-container">
            <table>
              <thead>
                <tr>
                  <th>No.</th>
                  <th>Videos/Documents</th>
                  <th>Date</th>
                  <th>Price</th>
                  <th>Status</th>
                  <th>Action</th>
                </tr>
              </thead>
              <tbody>
                {loading.content ? (
                  <tr>
                    <td colSpan="6" style={{ textAlign: 'center', padding: '20px' }}>
                      Loading strategies...
                    </td>
                  </tr>
                ) : errors.content ? (
                  <tr>
                    <td colSpan="6" style={{ textAlign: 'center', padding: '20px', color: 'red' }}>
                      Error loading strategies: {errors.content}
                    </td>
                  </tr>
                ) : strategies.length > 0 ? (
                  strategies.slice(0, 2).map((item) => (
                    <StrategyRow
                      key={item.id}
                      item={item}
                      toggle={toggleStatus}
                      onViewDetails={handleViewDetails}
                    />
                  ))
                ) : (
                  <tr>
                    <td colSpan="6" style={{ textAlign: 'center', padding: '20px' }}>
                      No strategies found
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
        </div>

        <div className="section">
          <div className="section-header">
            <h3>New Requests</h3>
            <a href="/requests">View All Requests</a>
          </div>
          <div className="table-container">
            <table>
              <thead>
                <tr>
                  <th>Order Id</th>
                  <th>Video/Documents</th>
                  <th>Date</th>
                  <th>Price</th>
                  <th>Requested Amount</th>
                  <th>Requested Customer</th>
                  <th>Action</th>
                </tr>
              </thead>
              <tbody>
                {loading.requests ? (
                  <tr>
                    <td colSpan="7" style={{ textAlign: 'center', padding: '20px' }}>
                      Loading requests...
                    </td>
                  </tr>
                ) : errors.requests ? (
                  <tr>
                    <td colSpan="7" style={{ textAlign: 'center', padding: '20px', color: 'red' }}>
                      Error loading requests: {errors.requests}
                    </td>
                  </tr>
                ) : formattedRequests.length > 0 ? (
                  formattedRequests.map((item) => (
                    <RequestRow key={item.id} item={item} showUser={true} />
                  ))
                ) : (
                  <tr>
                    <td colSpan="7" style={{ textAlign: 'center', padding: '20px' }}>
                      No requests found
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
        </div>

        <div className="section">
          <div className="section-header">
            <h3>New Bids</h3>
            <a href="/bids">View All Bids</a>
          </div>
          <div className="table-container">
            <table>
              <thead>
                <tr>
                  <th>Bid Id</th>
                  <th>Video/Documents</th>
                  <th>Date</th>
                  <th>Price</th>
                  <th>Bid Amount</th>
                  <th>Action</th>
                </tr>
              </thead>
              <tbody>
                {loading.bids ? (
                  <tr>
                    <td colSpan="6" style={{ textAlign: 'center', padding: '20px' }}>
                      Loading bids...
                    </td>
                  </tr>
                ) : errors.bids ? (
                  <tr>
                    <td colSpan="6" style={{ textAlign: 'center', padding: '20px', color: 'red' }}>
                      Error loading bids: {errors.bids}
                    </td>
                  </tr>
                ) : formattedBids.length > 0 ? (
                  formattedBids.map((item) => (
                    <BidRow key={item.id} item={item} />
                  ))
                ) : (
                  <tr>
                    <td colSpan="6" style={{ textAlign: 'center', padding: '20px' }}>
                      No bids found
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </SellerLayout>
  );
};

export default SellerDashboard;
