/* Custom PDF Viewer Styles */
.custom-pdf-viewer {
  display: flex;
  flex-direction: column;
  background: var(--white);
  border-radius: var(--border-radius-medium);
  overflow: hidden;
  box-shadow: var(--box-shadow);
}

/* Toolbar */
.custom-pdf-viewer__toolbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--smallfont) var(--basefont);
  background: var(--light-gray);
  border-bottom: 1px solid var(--border-color);
  flex-wrap: wrap;
  gap: var(--smallfont);
}

.custom-pdf-viewer__nav-controls,
.custom-pdf-viewer__zoom-controls,
.custom-pdf-viewer__action-controls {
  display: flex;
  align-items: center;
  gap: var(--extrasmallfont);
}

.custom-pdf-viewer__btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--extrasmallfont) var(--smallfont);
  background: var(--white);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  color: var(--text-color);
  font-size: var(--smallfont);
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 32px;
  height: 32px;
}

.custom-pdf-viewer__btn:hover:not(:disabled) {
  background: var(--btn-color);
  color: var(--white);
  border-color: var(--btn-color);
}

.custom-pdf-viewer__btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background: var(--light-gray);
}

.custom-pdf-viewer__page-info,
.custom-pdf-viewer__zoom-info {
  padding: var(--extrasmallfont) var(--smallfont);
  background: var(--white);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  font-size: var(--smallfont);
  color: var(--text-color);
  min-width: 60px;
  text-align: center;
  font-weight: 500;
}

.custom-pdf-viewer__fallback-info {
  padding: var(--extrasmallfont) var(--smallfont);
  background: var(--light-gray);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  font-size: var(--smallfont);
  color: var(--dark-gray);
  font-style: italic;
}

/* Document Container */
.custom-pdf-viewer__document {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: flex-start;
  padding: var(--basefont);
  overflow: auto;
  background: var(--light-gray);
}

/* Loading State */
.custom-pdf-viewer__loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  gap: var(--basefont);
  color: var(--dark-gray);
}

.custom-pdf-viewer__spinner {
  width: 40px;
  height: 40px;
  border: 3px solid var(--light-gray);
  border-top: 3px solid var(--btn-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Error State */
.custom-pdf-viewer__error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  gap: var(--basefont);
  color: var(--error-color, #dc3545);
  text-align: center;
  padding: var(--basefont);
}

.custom-pdf-viewer__retry-btn {
  padding: var(--smallfont) var(--basefont);
  background: var(--btn-color);
  color: var(--white);
  border: none;
  border-radius: var(--border-radius);
  cursor: pointer;
  font-size: var(--smallfont);
  transition: background-color 0.2s ease;
}

.custom-pdf-viewer__retry-btn:hover {
  background: var(--btn-hover-color, #0056b3);
}

/* PDF Page Styles */
.react-pdf__Page {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin: 0 auto;
  background: var(--white);
}

.react-pdf__Page__canvas {
  display: block;
  max-width: 100%;
  height: auto;
}

/* Responsive Design */
@media (max-width: 768px) {
  .custom-pdf-viewer__toolbar {
    flex-direction: column;
    align-items: stretch;
    gap: var(--smallfont);
  }

  .custom-pdf-viewer__nav-controls,
  .custom-pdf-viewer__zoom-controls,
  .custom-pdf-viewer__action-controls {
    justify-content: center;
  }

  .custom-pdf-viewer__btn {
    min-width: 40px;
    height: 40px;
    font-size: var(--basefont);
  }

  .custom-pdf-viewer__page-info,
  .custom-pdf-viewer__zoom-info {
    min-width: 80px;
    font-size: var(--basefont);
  }

  .custom-pdf-viewer__document {
    padding: var(--smallfont);
  }
}

@media (max-width: 480px) {
  .custom-pdf-viewer__toolbar {
    padding: var(--extrasmallfont);
  }

  .custom-pdf-viewer__btn {
    min-width: 36px;
    height: 36px;
    padding: var(--extrasmallfont);
  }

  .custom-pdf-viewer__page-info,
  .custom-pdf-viewer__zoom-info {
    min-width: 70px;
    padding: var(--extrasmallfont);
  }
}
